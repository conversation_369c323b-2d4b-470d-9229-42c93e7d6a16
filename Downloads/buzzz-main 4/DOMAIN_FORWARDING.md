# Domain Forwarding Feature

## Overview

The Domain Forwarding feature allows premium users to use their own custom domains for their business cards. This provides a more professional and branded experience for users who want to share their business cards using their own domain names.

## Features

### For Premium Users
- **Custom Domain Support**: Add up to 5 domains (Unlimited plans) or 10 domains (Super Unlimited plans)
- **Domain Verification**: Multiple verification methods (DNS, File, Meta tag)
- **Automatic SSL**: SSL certificates are automatically provisioned for verified domains
- **Domain Management**: Easy-to-use interface to add, verify, and manage domains

### Domain Limits by Plan
- **Free Plan**: 0 domains
- **Unlimited Monthly/Yearly**: 5 domains
- **Super Unlimited**: 10 domains
- **Super Admin**: Unlimited domains + can manage all domains across the platform

## How It Works

### 1. Domain Addition
Users can add custom domains through the dashboard:
1. Navigate to "Custom Domains" in the dashboard
2. Click "Add Domain"
3. Enter the domain name (e.g., `example.com`)
4. The system validates the domain format and creates a verification token

### 2. Domain Verification
Three verification methods are supported:

#### DNS Verification (Recommended)
- Add a CNAME record pointing your domain to `buzzz.my`
- Example: `example.com CNAME buzzz.my`
- The system automatically detects and verifies the DNS record

#### File Verification
- Upload a verification file to `https://example.com/.well-known/buzzz-verification.txt`
- The file should contain the verification token

#### Meta Tag Verification
- Add a meta tag to your website's HTML head section
- Example: `<meta name="buzzz-verification" content="your-verification-token">`

### 3. Domain Activation
Once verified:
- The domain is marked as verified and active
- SSL certificate is automatically provisioned
- The domain becomes accessible for the business card

## Technical Implementation

### Database Schema
```sql
-- Custom domains table
CREATE TABLE custom_domains (
  id uuid PRIMARY KEY,
  user_id uuid REFERENCES auth.users(id),
  business_card_id uuid REFERENCES business_cards(id),
  domain text UNIQUE NOT NULL,
  is_verified boolean DEFAULT false,
  is_active boolean DEFAULT false,
  verification_token text,
  verification_expires_at timestamptz,
  ssl_certificate_status text DEFAULT 'pending',
  dns_records jsonb DEFAULT '[]',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  deleted_at timestamptz DEFAULT null
);

-- Domain verifications table
CREATE TABLE domain_verifications (
  id uuid PRIMARY KEY,
  domain_id uuid REFERENCES custom_domains(id),
  verification_type text CHECK (verification_type IN ('dns', 'file', 'meta')),
  status text DEFAULT 'pending',
  verification_data jsonb DEFAULT '{}',
  verified_at timestamptz,
  created_at timestamptz DEFAULT now()
);
```

### Routing Logic
The application handles custom domains through the following flow:

1. **Request comes in** for a custom domain (e.g., `https://example.com`)
2. **BusinessCardView component** checks if the hostname matches a verified custom domain
3. **If match found**: Loads the associated business card
4. **If no match**: Falls back to username-based routing

### Security Features
- **Row Level Security (RLS)**: Users can only manage their own domains (except super admins who can manage all)
- **Domain Validation**: Strict format validation for domain names
- **Verification Tokens**: Cryptographically secure tokens for domain verification
- **Rate Limiting**: Protection against abuse in domain operations
- **Admin Override**: Super admins have full access to manage all domains across the platform

## Setup Instructions

### For Users

1. **Purchase a Premium Plan**: Upgrade to Unlimited or Super Unlimited plan
2. **Add Your Domain**: Go to Dashboard → Custom Domains → Add Domain
3. **Configure DNS**: Add the required CNAME record in your domain provider
4. **Verify Domain**: Click "Verify" in the dashboard
5. **Wait for SSL**: SSL certificate will be provisioned automatically

### DNS Configuration Examples

#### Cloudflare
```
Type: CNAME
Name: @ (or your subdomain)
Target: buzzz.my
Proxy: Disabled (Gray cloud)
```

#### GoDaddy
```
Type: CNAME
Host: @ (or your subdomain)
Points to: buzzz.my
TTL: 600 (or default)
```

#### Namecheap
```
Type: CNAME Record
Host: @ (or your subdomain)
Value: buzzz.my
TTL: Automatic
```

## Troubleshooting

### Common Issues

1. **Domain Not Verifying**
   - Check that DNS changes have propagated (can take up to 48 hours)
   - Ensure CNAME record is correctly configured
   - Verify domain format is correct (no http:// or www)

2. **SSL Certificate Issues**
   - SSL certificates are provisioned automatically after verification
   - Allow up to 24 hours for SSL certificate to be active
   - Contact support if SSL issues persist

3. **Domain Limit Reached**
   - Upgrade to a higher plan to add more domains
   - Delete unused domains to free up slots

### Support

For technical support with domain forwarding:
- Check the FAQ section
- Contact support through the dashboard
- Provide domain name and error details when reporting issues

## Future Enhancements

- **Subdomain Support**: Allow users to use subdomains (e.g., `card.example.com`)
- **Wildcard SSL**: Support for wildcard SSL certificates
- **Domain Analytics**: Track domain-specific analytics
- **Bulk Domain Management**: Add multiple domains at once
- **Domain Transfer**: Transfer domains between accounts 