import { BusinessCard } from '../types';

export function generateVCard(card: BusinessCard): string {
  const vcard = [
    'BEGIN:VCARD',
    'VERSION:3.0',
    `FN:${card.name}`,
    `N:${card.name.split(' ').reverse().join(';')};;;`,
    `ORG:${card.company}`,
    `TITLE:${card.title}`,
    `EMAIL:${card.email}`,
    `TEL:${card.phone}`,
    `URL:${card.website}`,
    `NOTE:${card.bio}`,
  ];

  // Add social media URLs as additional URLs
  card.socialLinks.forEach(link => {
    if (link.url) {
      vcard.push(`URL:${link.url}`);
    }
  });

  // Add profile image if available
  if (card.profileImage && card.profileImage.startsWith('http')) {
    vcard.push(`PHOTO:${card.profileImage}`);
  }

  vcard.push('END:VCARD');
  
  return vcard.join('\r\n');
}

export function downloadVCard(card: BusinessCard): void {
  const vCardData = generateVCard(card);
  const blob = new Blob([vCardData], { type: 'text/vcard;charset=utf-8' });
  const url = window.URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = `${card.name.replace(/\s+/g, '-')}-contact.vcf`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // Clean up the URL object
  window.URL.revokeObjectURL(url);
}