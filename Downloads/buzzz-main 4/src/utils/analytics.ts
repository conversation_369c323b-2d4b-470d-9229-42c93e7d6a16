import { supabase } from '../lib/supabase';

export type AnalyticsEvent = 'card_view' | 'contact_click' | 'offer_click' | 'social_click';

interface AnalyticsData {
  businessCardId: string;
  eventType: AnalyticsEvent;
  eventData?: any;
  userId?: string;
}

export async function trackAnalytics({ businessCardId, eventType, eventData = {}, userId }: AnalyticsData) {
  try {
    // Get additional context
    const userAgent = navigator.userAgent;
    const referrer = document.referrer;
    
    // Remove external IP API call to improve performance
    // IP address will be captured by Supabase automatically if needed

    const { error } = await supabase
      .from('user_analytics')
      .insert({
        user_id: userId,
        business_card_id: businessCardId,
        event_type: eventType,
        event_data: eventData,
        user_agent: userAgent,
        referrer: referrer || null
      });

    if (error) {
      console.error('Analytics tracking error:', error);
    }
  } catch (error) {
    console.error('Failed to track analytics:', error);
  }
}

export async function getAnalytics(userId: string, businessCardId?: string, days: number = 30) {
  try {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    let query = supabase
      .from('user_analytics')
      .select('*')
      .eq('user_id', userId)
      .gte('created_at', startDate.toISOString());

    if (businessCardId) {
      query = query.eq('business_card_id', businessCardId);
    }

    const { data, error } = await query.order('created_at', { ascending: false });

    if (error) throw error;

    // Process analytics data
    const analytics = {
      totalViews: data?.filter(item => item.event_type === 'card_view').length || 0,
      contactClicks: data?.filter(item => item.event_type === 'contact_click').length || 0,
      offerClicks: data?.filter(item => item.event_type === 'offer_click').length || 0,
      socialClicks: data?.filter(item => item.event_type === 'social_click').length || 0,
      rawData: data || []
    };

    // Calculate growth percentages (comparing to previous period)
    const previousStartDate = new Date(startDate);
    previousStartDate.setDate(previousStartDate.getDate() - days);

    let previousQuery = supabase
      .from('user_analytics')
      .select('*')
      .eq('user_id', userId)
      .gte('created_at', previousStartDate.toISOString())
      .lt('created_at', startDate.toISOString());

    if (businessCardId) {
      previousQuery = previousQuery.eq('business_card_id', businessCardId);
    }

    const { data: previousData } = await previousQuery;

    const previousAnalytics = {
      totalViews: previousData?.filter(item => item.event_type === 'card_view').length || 0,
      contactClicks: previousData?.filter(item => item.event_type === 'contact_click').length || 0,
      offerClicks: previousData?.filter(item => item.event_type === 'offer_click').length || 0,
      socialClicks: previousData?.filter(item => item.event_type === 'social_click').length || 0
    };

    // Calculate growth percentages
    const calculateGrowth = (current: number, previous: number) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return Math.round(((current - previous) / previous) * 100);
    };

    return {
      ...analytics,
      growth: {
        totalViews: calculateGrowth(analytics.totalViews, previousAnalytics.totalViews),
        contactClicks: calculateGrowth(analytics.contactClicks, previousAnalytics.contactClicks),
        offerClicks: calculateGrowth(analytics.offerClicks, previousAnalytics.offerClicks),
        socialClicks: calculateGrowth(analytics.socialClicks, previousAnalytics.socialClicks)
      }
    };
  } catch (error) {
    console.error('Error fetching analytics:', error);
    return {
      totalViews: 0,
      contactClicks: 0,
      offerClicks: 0,
      socialClicks: 0,
      growth: { totalViews: 0, contactClicks: 0, offerClicks: 0, socialClicks: 0 },
      rawData: []
    };
  }
}