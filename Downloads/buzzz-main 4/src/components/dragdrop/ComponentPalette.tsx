import React, { useState } from 'react';
import { useDraggable } from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import { 
  User, 
  FileText, 
  Mail, 
  Share2, 
  Gift, 
  MapPin, 
  Plus,
  Star,
  Globe,
  ExternalLink,
  Minus,
} from 'lucide-react';
import { CardComponent } from './types';

interface ComponentPaletteProps {
  onComponentAdd?: (component: CardComponent) => void;
  hasPermission: (permission: string) => boolean;
  onUpgradeClick?: () => void;
}

const availableComponents: CardComponent[] = [
  {
    id: 'palette-profile',
    type: 'profile',
    title: 'Profile Section',
    description: 'Name, title, company, and avatar',
    icon: 'User',
    isActive: true,
    order: 0,
    config: {
      showAvatar: true,
      showName: true,
      showTitle: true,
      showCompany: true,
      profileImage: ''
    }
  },
  {
    id: 'palette-bio',
    type: 'bio',
    title: 'Bio Section',
    description: 'Personal description and story',
    icon: 'FileText',
    isActive: true,
    order: 1,
    config: {
      maxLength: 200,
      showReadMore: true
    }
  },
  {
    id: 'palette-contact',
    type: 'contact',
    title: 'Contact Info',
    description: 'Email, phone, and website',
    icon: 'Mail',
    isActive: true,
    order: 2,
    config: {
      showEmail: true,
      showPhone: true,
      showWebsite: true
    }
  },
  {
    id: 'palette-social',
    type: 'social',
    title: 'Social Links',
    description: 'Social media and professional links',
    icon: 'Share2',
    isActive: true,
    order: 3,
    config: {
      platforms: ['linkedin', 'twitter', 'instagram', 'facebook'],
      layout: 'grid'
    }
  },
  {
    id: 'palette-offers',
    type: 'offers',
    title: 'Special Offer',
    description: 'Single offer or promotion',
    icon: 'Gift',
    isActive: true,
    order: 4,
    config: {
      title: 'Special Offer',
      subtitle: 'Limited time offer',
      offerTitle: '20% Off First Order',
      offerDescription: 'Get 20% off your first purchase with us',
      offerButtonText: 'Claim Offer',
      offerUrl: 'https://example.com/offer',
      landingPageHeaderImage: '',
      landingPageTitle: 'Special Offer Details',
      landingPageSubtitle: 'Limited time offer',
      landingPageContent: 'Add your offer details here...',
      landingPageCtaText: 'Get Started',
                landingPageCtaUrl: 'https://example.com/signup',
          landingPageBgColor: '#ffffff',
          landingPageTextColor: '#000000',
          landingPageCtaButtonColor: '#2563eb',
          landingPageCtaTextColor: '#ffffff'
    }
  },
    {
      id: 'palette-link',
      type: 'link',
      title: 'Link',
      description: 'Simple link to external websites or pages',
      icon: 'ExternalLink',
      isActive: true,
      order: 6,
      config: {
        title: 'Visit Our Website',
        url: 'https://example.com',
        description: 'Click to visit our website'
      }
    },
    {
      id: 'palette-location',
      type: 'location',
      title: 'Location & Join Date',
      description: 'Location and company join information',
      icon: 'MapPin',
      isActive: true,
      order: 7,
      config: {
        showLocation: true,
        showJoinYear: true
      }
    },
  {
    id: 'palette-text',
    type: 'text',
    title: 'Custom Text/HTML',
    description: 'Rich text editor with HTML support like WordPress',
    icon: 'FileText',
    isActive: true,
    order: 8,
    config: {
      htmlContent: '<p>Add your custom content here...</p><p>You can use <strong>HTML formatting</strong>, add <a href="#">links</a>, and more!</p>',
      showHtmlEditor: false,
      customCss: '',
      allowHtml: true,
      backgroundColor: '#ffffff',
      textColor: '#000000',
      borderRadius: 12,
      padding: 16,
      alignment: 'left'
    }
  },
  {
    id: 'palette-divider',
    type: 'divider',
    title: 'Divider',
    description: 'Separate sections with stylish dividers',
    icon: 'Minus',
    isActive: true,
    order: 9,
    config: {
      dividerStyle: 'solid',
      dividerColor: '#e5e7eb',
      dividerThickness: 1,
      dividerWidth: 100,
      dividerMargin: 30
    }
  },

];

const iconMap = {
  User,
  FileText,
  Mail,
  Share2,
  Gift,
  MapPin,
  Plus,
  Star,
  Globe,
  ExternalLink,
  Minus,
};

function DraggableComponent({ component, onAdd }: { component: CardComponent; onAdd?: (component: CardComponent) => void }) {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: component.id,
    data: component
  });

  const [isAdding, setIsAdding] = useState(false);

  const style = {
    transform: CSS.Translate.toString(transform),
  };

  const handleAdd = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (onAdd) {
      setIsAdding(true);
      onAdd(component);
      // Reset the adding state after a short delay
      setTimeout(() => setIsAdding(false), 1000);
    }
  };

  const IconComponent = iconMap[component.icon as keyof typeof iconMap];

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`group bg-white rounded-xl p-4 border-2 border-gray-100 hover:border-primary-200 hover:shadow-md transition-all duration-200 hover:-translate-y-1 ${
        isDragging ? 'opacity-50 scale-105 shadow-lg' : ''
      }`}
    >
      <div className="flex items-center space-x-3">
        <div 
          {...listeners}
          {...attributes}
          className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-primary-100 to-primary-200 rounded-lg flex items-center justify-center group-hover:from-primary-200 group-hover:to-primary-300 transition-all duration-200 cursor-grab active:cursor-grabbing"
        >
          {IconComponent && <IconComponent className="w-5 h-5 text-primary-600" />}
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="text-sm font-semibold text-gray-900 group-hover:text-primary-700 transition-colors duration-200">
            {component.title}
          </h3>
          <p className="text-xs text-gray-500 mt-1">
            {component.description}
          </p>
        </div>
        <div className="flex-shrink-0">
          <button
            onClick={handleAdd}
            disabled={isAdding}
            className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200 shadow-md touch-manipulation cursor-pointer ${
              isAdding 
                ? 'bg-green-500 text-white scale-110' 
                : 'bg-primary-500 hover:bg-primary-600 active:bg-primary-700 text-white hover:scale-110 active:scale-95 hover:shadow-lg'
            }`}
            title={isAdding ? "Added!" : "Add component"}
          >
            {isAdding ? (
              <span className="text-xs font-bold">✓</span>
            ) : (
              <Plus className="w-4 h-4" />
            )}
          </button>
        </div>
      </div>
    </div>
  );
}

export default function ComponentPalette({ onComponentAdd, hasPermission, onUpgradeClick }: ComponentPaletteProps) {
  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-soft border border-white/50 p-6">
      <div className="flex items-center mb-6">
        <Plus className="w-5 h-5 text-primary-500 mr-3" />
        <h2 className="text-lg font-bold text-gray-900">Add Components</h2>
      </div>
      
      <div className="space-y-3">
        {availableComponents
          .filter(component => {
            // Basic components always available
            if (['profile', 'bio', 'contact', 'social'].includes(component.type)) {
              return true;
            }
            
            // Offers are available to everyone (but limited to 3 for free users)
            if (component.type === 'offers') {
              return true;
            }
            
            // Custom text components are available to everyone (but limited to 2 for free users)
            if (component.type === 'text') {
              return true;
            }
            
            // Link components are basic
            if (component.type === 'link') {
              return true;
            }
            
            // Location components are basic
            if (component.type === 'location') {
              return true;
            }
            
            return true;
          })
          .map((component) => (
            <DraggableComponent 
              key={component.id} 
              component={component}
              onAdd={onComponentAdd}
            />
          ))}
      </div>
      
      <div className="mt-6 pt-4 border-t border-gray-100">
        <p className="text-xs text-gray-500 text-center">
          <span className="hidden sm:inline">Drag components to your card or click the + button</span>
          <span className="sm:hidden">Tap the + button to add components</span>
        </p>
        
        {/* Upgrade prompts for restricted features */}
        {!hasPermission('unlimited_offers') && (
          <div className="mt-4 p-3 bg-gradient-to-r from-amber-50 to-orange-50 rounded-lg border border-amber-200">
            <p className="text-xs text-amber-700 text-center">
              💡 <strong>Free Plan Limits:</strong> 3 offers • 3 links • 2 custom text components
            </p>
            <p className="text-xs text-amber-600 text-center mt-1">
              ✨ Upgrade for unlimited components and premium features!
            </p>
            {onUpgradeClick && (
              <button
                onClick={onUpgradeClick}
                className="mt-2 w-full text-xs bg-amber-500 hover:bg-amber-600 text-white px-3 py-1.5 rounded-md transition-colors"
              >
                Upgrade Now
              </button>
            )}
          </div>
        )}
        
        {!hasPermission('change_background') && (
          <div className="mt-4 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
            <p className="text-xs text-blue-700 text-center">
              🎨 <strong>Unlock premium customization!</strong> Upgrade to change backgrounds and unlock unlimited custom components.
            </p>
            {onUpgradeClick && (
              <button
                onClick={onUpgradeClick}
                className="mt-2 w-full text-xs bg-blue-500 hover:bg-blue-600 text-white px-3 py-1.5 rounded-md transition-colors"
              >
                Upgrade Now
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
