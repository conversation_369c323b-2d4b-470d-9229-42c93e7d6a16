import React, { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'

interface User {
  id: string
  email: string
  full_name?: string
  created_at: string
}

interface EmailTemplate {
  id: string
  name: string
  subject: string
  html_content: string
}

export default function AdminEmailDashboard() {
  const [users, setUsers] = useState<User[]>([])
  const [templates, setTemplates] = useState<EmailTemplate[]>([])
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [selectedTemplate, setSelectedTemplate] = useState<string>('')
  const [customSubject, setCustomSubject] = useState('')
  const [customMessage, setCustomMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState('')

  // Fetch users from database
  useEffect(() => {
    fetchUsers()
    fetchTemplates()
  }, [])

  // Create sample templates if none exist
  useEffect(() => {
    const createSampleTemplates = async () => {
      try {
        const { data: existingTemplates } = await supabase
          .from('email_templates')
          .select('id')
          .limit(1)

        if (!existingTemplates || existingTemplates.length === 0) {
          // Create sample templates
          const sampleTemplates = [
            {
              name: 'Welcome Email',
              subject: 'Welcome to Our Platform!',
              html_content: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                  <h2 style="color: #0B1B2A;">Welcome to Our Platform!</h2>
                  <p>Hi there,</p>
                  <p>Welcome to our business card platform! We're excited to have you on board.</p>
                  <p>Best regards,<br>The Team</p>
                </div>
              `,
              description: 'A friendly welcome email for new users'
            },
            {
              name: 'Platform Update',
              subject: 'New Features Available!',
              html_content: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                  <h2 style="color: #0B1B2A;">New Features Available!</h2>
                  <p>Hi there,</p>
                  <p>We've added some exciting new features to our platform. Check them out!</p>
                  <p>Best regards,<br>The Team</p>
                </div>
              `,
              description: 'Announcement email for platform updates'
            }
          ]

          for (const template of sampleTemplates) {
            await supabase
              .from('email_templates')
              .insert(template)
          }

          // Refresh templates after creating samples
          fetchTemplates()
        }
      } catch (error) {
        console.error('Error creating sample templates:', error)
      }
    }

    createSampleTemplates()
  }, [])

  const fetchUsers = async () => {
    try {
      // Get the current session
      const { data: { session } } = await supabase.auth.getSession()
      if (!session?.access_token) {
        throw new Error('No access token available')
      }

      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
      if (!supabaseUrl) {
        throw new Error('VITE_SUPABASE_URL environment variable is not set')
      }

      // Call the admin-users edge function to get users with emails
      const response = await fetch(`${supabaseUrl}/functions/v1/admin-users`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.warn('Edge function failed, using fallback method:', errorText)
        throw new Error(`HTTP ${response.status}: ${errorText}`)
      }

      const data = await response.json()
      const usersWithEmails = (data.users || []).map((user: any) => ({
        id: user.id,
        email: user.email || 'No email',
        full_name: user.profile?.userType || 'User',
        created_at: user.created_at
      }))
      
      setUsers(usersWithEmails)
    } catch (error) {
      console.error('Error fetching users:', error)
      
      // Fallback: get basic user profile information without emails
      try {
        const { data, error: fallbackError } = await supabase
          .from('user_profiles')
          .select('user_id, created_at, user_type')
          .order('created_at', { ascending: false })

        if (fallbackError) throw fallbackError
        
        // Create user objects with basic information
        const basicUsers = (data || []).map(profile => ({
          id: profile.user_id,
          email: `user-${profile.user_id.slice(0, 8)}@example.com`, // Placeholder email
          full_name: `User (${profile.user_type})`,
          created_at: profile.created_at
        }))
        
        setUsers(basicUsers)
        console.warn('Using fallback user data (no emails available)')
      } catch (fallbackError) {
        console.error('Fallback method also failed:', fallbackError)
        setUsers([])
      }
    }
  }

  const fetchTemplates = async () => {
    try {
      const { data, error } = await supabase
        .from('email_templates')
        .select('*')
        .order('name')

      if (error) throw error
      setTemplates(data || [])
    } catch (error) {
      console.error('Error fetching templates:', error)
    }
  }

  const sendEmails = async () => {
    if (selectedUsers.length === 0) {
      setMessage('Please select at least one user')
      return
    }

    setIsLoading(true)
    setMessage('')

    try {
      const selectedTemplateData = templates.find(t => t.id === selectedTemplate)
      const subject = customSubject || selectedTemplateData?.subject || 'Message from Business Card Platform'
      const htmlContent = selectedTemplateData?.html_content || customMessage
      
      console.log('Starting to send emails...')
      console.log('Selected users:', selectedUsers)
      console.log('Subject:', subject)
      console.log('HTML content length:', htmlContent?.length || 0)
      
      // Send email to each selected user
      for (const userId of selectedUsers) {
        const user = users.find(u => u.id === userId)
        if (!user?.email || user.email === 'No email') continue

        console.log(`Sending email to: ${user.email}`)
        
        const session = await supabase.auth.getSession()
        const accessToken = session.data.session?.access_token
        
        console.log('Access token available:', !!accessToken)
        console.log('Token length:', accessToken?.length || 0)

        const requestBody = {
          to: user.email,
          subject: subject,
          html: htmlContent
        }
        
        console.log('Request body:', requestBody)

        const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/send-email`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${accessToken}`
          },
          body: JSON.stringify(requestBody)
        })

        console.log('Response status:', response.status)
        console.log('Response headers:', Object.fromEntries(response.headers.entries()))

        if (!response.ok) {
          const errorText = await response.text()
          console.log('Error response text:', errorText)
          
          let errorMessage = 'Unknown error'
          try {
            const errorData = JSON.parse(errorText)
            errorMessage = errorData.message || errorData.error || 'Unknown error'
          } catch (e) {
            errorMessage = errorText || 'Unknown error'
          }
          
          throw new Error(`Failed to send email to ${user.email}: ${errorMessage}`)
        }

        const responseData = await response.json()
        console.log('Success response:', responseData)
      }

      setMessage(`Successfully sent emails to ${selectedUsers.length} users!`)
      setSelectedUsers([])
      setCustomSubject('')
      setCustomMessage('')
    } catch (error) {
      console.error('Error sending emails:', error)
      setMessage(`Error sending emails: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsLoading(false)
    }
  }

  const toggleUserSelection = (userId: string) => {
    setSelectedUsers(prev => 
      prev.includes(userId) 
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    )
  }

  const selectAllUsers = () => {
    setSelectedUsers(users.map(u => u.id))
  }

  const deselectAllUsers = () => {
    setSelectedUsers([])
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Admin Email Dashboard</h1>
        <p className="text-gray-600">Send emails to your platform users</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Column - User Selection */}
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900">Select Users</h2>
              <div className="space-x-2">
                <button
                  onClick={selectAllUsers}
                  className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200"
                >
                  Select All
                </button>
                <button
                  onClick={deselectAllUsers}
                  className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
                >
                  Clear
                </button>
              </div>
            </div>

            <div className="space-y-2 max-h-96 overflow-y-auto">
              {users.map(user => (
                <label key={user.id} className="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-md cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedUsers.includes(user.id)}
                    onChange={() => toggleUserSelection(user.id)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">{user.full_name || 'No Name'}</div>
                    <div className="text-sm text-gray-500">{user.email}</div>
                    <div className="text-xs text-gray-400">
                      Joined: {new Date(user.created_at).toLocaleDateString()}
                    </div>
                  </div>
                </label>
              ))}
            </div>

            <div className="mt-4 text-sm text-gray-600">
              {selectedUsers.length} user(s) selected
            </div>
          </div>
        </div>

        {/* Right Column - Email Configuration */}
        <div className="space-y-6">
          {/* Template Selection */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Email Template</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Choose Template
                </label>
                <select
                  value={selectedTemplate}
                  onChange={(e) => setSelectedTemplate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Custom Message</option>
                  {templates.map(template => (
                    <option key={template.id} value={template.id}>
                      {template.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Subject Line
                </label>
                <input
                  type="text"
                  value={customSubject}
                  onChange={(e) => setCustomSubject(e.target.value)}
                  placeholder="Enter email subject..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Custom Message (if no template selected)
                </label>
                <textarea
                  value={customMessage}
                  onChange={(e) => setCustomMessage(e.target.value)}
                  placeholder="Enter your message here..."
                  rows={6}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Send Button */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <button
              onClick={sendEmails}
              disabled={isLoading || selectedUsers.length === 0}
              className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? 'Sending Emails...' : `Send Email${selectedUsers.length > 1 ? 's' : ''} to ${selectedUsers.length} User${selectedUsers.length > 1 ? 's' : ''}`}
            </button>

            {message && (
              <div className={`mt-4 p-3 rounded-md ${
                message.includes('Successfully') 
                  ? 'bg-green-100 text-green-700' 
                  : 'bg-red-100 text-red-700'
              }`}>
                {message}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
