import React, { useState } from 'react';
import { <PERSON>ap, Star, Crown, ArrowRight, Check, Loader2, Ticket } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { stripeProducts } from '../stripe-config';

interface UpgradePromptProps {
  feature: string;
  requiredPlan: 'unlimited' | 'super_unlimited';
  onClose?: () => void;
}

export default function UpgradePrompt({ feature, requiredPlan, onClose }: UpgradePromptProps) {
  const { userProfile, session } = useAuth();
  const [loading, setLoading] = useState<string | null>(null);

  const plans = {
    unlimited: {
      monthly: stripeProducts.find(p => p.name === 'Unlimited (Monthly)')!,
      yearly: stripeProducts.find(p => p.name === 'Unlimited (Yearly)')!
    },
    super_unlimited: stripeProducts.find(p => p.name === 'Super Unlimited')!
  };

  const features = {
    unlimited: [
      'Unlimited business cards',
      'Unlimited offers',
      'Advanced analytics',
      'Custom backgrounds',
      'Priority support'
    ],
    super_unlimited: [
      'Everything in Unlimited',
      'E-commerce integration',
      'Payment processing',
      'Advanced customization',
      'White-label options',
      '24/7 dedicated support'
    ]
  };

  const handleUpgrade = async (priceId: string, planName: string) => {
    if (!session?.access_token) {
      console.error('No access token available');
      return;
    }

    setLoading(priceId);

    try {
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe-checkout`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          price_id: priceId,
          mode: 'subscription',
          success_url: `${window.location.origin}/dashboard?success=true&plan=${encodeURIComponent(planName)}`,
          cancel_url: `${window.location.origin}/dashboard?canceled=true`
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create checkout session');
      }

      const { url } = await response.json();
      
      if (url) {
        window.location.href = url;
      } else {
        throw new Error('No checkout URL received');
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      alert('Failed to start checkout process. Please try again.');
    } finally {
      setLoading(null);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Upgrade Required
              </h2>
              <p className="text-gray-600">
                {feature} is available with {requiredPlan === 'unlimited' ? 'Unlimited' : 'Super Unlimited'} plans
              </p>
            </div>
            {onClose && (
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
        </div>

        {/* Plans */}
        <div className="p-6">
          {requiredPlan === 'unlimited' ? (
            <div className="grid md:grid-cols-2 gap-6">
              {/* Monthly Plan */}
              <div className="border border-gray-200 rounded-xl p-6 hover:border-green-300 transition-colors">
                <div className="flex items-center mb-4">
                  <Star className="w-6 h-6 text-green-600 mr-3" />
                  <h3 className="text-xl font-bold text-gray-900">{plans.unlimited.monthly.name}</h3>
                </div>
                <div className="mb-6">
                  <span className="text-3xl font-bold text-gray-900">${plans.unlimited.monthly.price}</span>
                  <span className="text-gray-600">/{plans.unlimited.monthly.interval}</span>
                </div>
                <ul className="space-y-3 mb-6">
                  {features.unlimited.map((feature, index) => (
                    <li key={index} className="flex items-center">
                      <Check className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
                <button 
                  onClick={() => handleUpgrade(plans.unlimited.monthly.priceId, plans.unlimited.monthly.name)}
                  disabled={loading === plans.unlimited.monthly.priceId}
                  className="w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors font-semibold disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  {loading === plans.unlimited.monthly.priceId ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    'Choose Monthly'
                  )}
                </button>
              </div>

              {/* Yearly Plan */}
              <div className="border-2 border-blue-300 rounded-xl p-6 relative bg-blue-50">
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-blue-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </span>
                </div>
                <div className="flex items-center mb-4">
                  <Star className="w-6 h-6 text-blue-600 mr-3" />
                  <h3 className="text-xl font-bold text-gray-900">{plans.unlimited.yearly.name}</h3>
                </div>
                <div className="mb-2">
                  <span className="text-3xl font-bold text-gray-900">${plans.unlimited.yearly.price}</span>
                  <span className="text-gray-600">/{plans.unlimited.yearly.interval}</span>
                </div>
                <div className="mb-6">
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm font-medium">
                    Save ${(plans.unlimited.monthly.price * 12) - plans.unlimited.yearly.price}
                  </span>
                </div>
                <ul className="space-y-3 mb-6">
                  {features.unlimited.map((feature, index) => (
                    <li key={index} className="flex items-center">
                      <Check className="w-5 h-5 text-blue-500 mr-3 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
                <button 
                  onClick={() => handleUpgrade(plans.unlimited.yearly.priceId, plans.unlimited.yearly.name)}
                  disabled={loading === plans.unlimited.yearly.priceId}
                  className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-semibold disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  {loading === plans.unlimited.yearly.priceId ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    'Choose Yearly'
                  )}
                </button>
              </div>
            </div>
          ) : (
            /* Super Unlimited Plan */
            <div className="max-w-md mx-auto">
              <div className="border-2 border-yellow-300 rounded-xl p-6 bg-yellow-50">
                <div className="flex items-center mb-4">
                  <Zap className="w-6 h-6 text-yellow-600 mr-3" />
                  <h3 className="text-xl font-bold text-gray-900">{plans.super_unlimited.name}</h3>
                </div>
                <div className="mb-6">
                  <span className="text-3xl font-bold text-gray-900">${plans.super_unlimited.price}</span>
                  <span className="text-gray-600">/{plans.super_unlimited.interval}</span>
                </div>
                <ul className="space-y-3 mb-6">
                  {features.super_unlimited.map((feature, index) => (
                    <li key={index} className="flex items-center">
                      <Check className="w-5 h-5 text-yellow-500 mr-3 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
                <button 
                  onClick={() => handleUpgrade(plans.super_unlimited.priceId, plans.super_unlimited.name)}
                  disabled={loading === plans.super_unlimited.priceId}
                  className="w-full bg-yellow-600 text-white py-3 px-4 rounded-lg hover:bg-yellow-700 transition-colors font-semibold flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading === plans.super_unlimited.priceId ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      Upgrade to Super Unlimited
                      <ArrowRight className="w-5 h-5 ml-2" />
                    </>
                  )}
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-center">
            <p className="text-sm text-gray-600 mb-2">
              All plans include a 14-day free trial. Cancel anytime.
            </p>
            <p className="text-xs text-gray-500">
              Secure payment processing by Stripe
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}