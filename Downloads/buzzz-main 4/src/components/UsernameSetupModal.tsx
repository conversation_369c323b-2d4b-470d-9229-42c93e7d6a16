import React, { useState, useEffect } from 'react';
import { User, Check, X as XIcon, <PERSON><PERSON><PERSON>, AlertCircle } from 'lucide-react';
import { supabase } from '../lib/supabase';
import { isUsernameReserved } from '../utils/usernameBlacklist';

interface UsernameSetupModalProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: () => void;
  userEmail?: string;
  userName?: string;
}

export default function UsernameSetupModal({ 
  isOpen, 
  onClose, 
  onComplete, 
  userEmail,
  userName 
}: UsernameSetupModalProps) {
  const [username, setUsername] = useState('');
  const [usernameStatus, setUsernameStatus] = useState<'idle' | 'checking' | 'available' | 'taken'>('idle');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Generate initial username from Google profile
  useEffect(() => {
    if (isOpen && !username) {
      const generateUsername = () => {
        // Try to use the user's name first
        if (userName) {
          const cleanName = userName.toLowerCase()
            .replace(/[^a-zA-Z0-9\s]/g, '')
            .replace(/\s+/g, '')
            .substring(0, 20);
          if (cleanName.length >= 3) {
            return cleanName;
          }
        }
        
        // Fallback to email prefix
        if (userEmail) {
          const emailPrefix = userEmail.split('@')[0];
          return emailPrefix.toLowerCase()
            .replace(/[^a-zA-Z0-9]/g, '')
            .substring(0, 20);
        }
        
        // Final fallback
        return 'user';
      };

      const initialUsername = generateUsername();
      setUsername(initialUsername);
      checkUsernameAvailability(initialUsername);
    }
  }, [isOpen, userName, userEmail]);

  // Check username availability
  const checkUsernameAvailability = async (username: string) => {
    if (!username || username.length < 3) {
      setUsernameStatus('idle');
      return;
    }

    // Check if username is reserved first
    if (isUsernameReserved(username)) {
      setUsernameStatus('taken');
      return;
    }

    setUsernameStatus('checking');
    
    try {
      const { data: isAvailable, error } = await supabase
        .rpc('check_username_availability', { check_username: username });
      
      if (error) {
        console.error('Error checking username:', error);
        setUsernameStatus('taken');
        return;
      }
      
      setUsernameStatus(isAvailable ? 'available' : 'taken');
    } catch (err) {
      console.error('Error checking username availability:', err);
      setUsernameStatus('taken');
    }
  };

  // Debounced username availability check
  useEffect(() => {
    if (username) {
      const timeoutId = setTimeout(() => {
        checkUsernameAvailability(username);
      }, 500);

      return () => clearTimeout(timeoutId);
    } else {
      setUsernameStatus('idle');
    }
  }, [username]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    // Validate username format
    const usernameRegex = /^[a-zA-Z0-9_-]+$/;
    if (!usernameRegex.test(username)) {
      setError('Username can only contain letters, numbers, hyphens, and underscores');
      setLoading(false);
      return;
    }

    if (username.length < 3 || username.length > 30) {
      setError('Username must be between 3 and 30 characters');
      setLoading(false);
      return;
    }

    if (username.startsWith('-') || username.startsWith('_') || 
        username.endsWith('-') || username.endsWith('_')) {
      setError('Username cannot start or end with hyphens or underscores');
      setLoading(false);
      return;
    }

    // Check if username is reserved
    if (isUsernameReserved(username)) {
      setError('This username is reserved and cannot be used. Please choose a different one.');
      setLoading(false);
      return;
    }

    // Check if username is available
    if (usernameStatus === 'taken') {
      setError('Username is already taken. Please choose a different one.');
      setLoading(false);
      return;
    }

    if (usernameStatus === 'checking') {
      setError('Please wait while we check username availability.');
      setLoading(false);
      return;
    }

    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        setError('User not found. Please try signing in again.');
        setLoading(false);
        return;
      }

      // Update user profile with username
      const { error: updateError } = await supabase
        .from('user_profiles')
        .update({ 
          username,
          last_username_change: new Date().toISOString()
        })
        .eq('user_id', user.id);

      if (updateError) {
        console.error('Error updating username:', updateError);
        setError('Failed to set username. Please try again.');
        setLoading(false);
        return;
      }

      // Success!
      onComplete();
    } catch (err) {
      console.error('Error setting username:', err);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const generateNewUsername = () => {
    const base = userName ? userName.toLowerCase().replace(/[^a-zA-Z0-9]/g, '') : 'user';
    const randomSuffix = Math.random().toString(36).substring(2, 6);
    const newUsername = `${base}${randomSuffix}`.substring(0, 20);
    setUsername(newUsername);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-3xl shadow-large w-full max-w-md transform animate-scale-in">
        {/* Header */}
        <div className="relative p-8 pb-6">
          <div className="absolute inset-0 bg-gradient-to-br from-primary-50 to-secondary-50 rounded-t-3xl"></div>
          <div className="relative flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-neutral-900 flex items-center">
                <Sparkles className="w-6 h-6 mr-2 text-primary-500" />
                Choose Your Username
              </h2>
              <p className="text-neutral-600 mt-1">
                Set up your unique username for your digital business card
              </p>
            </div>
            <button
              onClick={onClose}
              className="w-10 h-10 bg-white/80 backdrop-blur-sm rounded-2xl flex items-center justify-center hover:bg-white transition-all duration-200 transform hover:scale-105"
            >
              <XIcon className="w-5 h-5 text-neutral-600" />
            </button>
          </div>
        </div>

        {/* Form */}
        <div className="px-8 pb-8 space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-2xl p-4 flex items-start animate-slide-in">
              <AlertCircle className="w-5 h-5 text-red-500 mr-3 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <p className="text-red-700 text-sm font-medium">{error}</p>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-5">
            <div>
              <label className="block text-sm font-semibold text-neutral-700 mb-2">
                Username
              </label>
              <div className="relative">
                <User className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" />
                <input
                  type="text"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  required
                  minLength={3}
                  maxLength={30}
                  pattern="[a-zA-Z0-9_-]+"
                  className={`w-full pl-12 pr-12 py-4 border rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-neutral-50 focus:bg-white ${
                    usernameStatus === 'available' 
                      ? 'border-green-300 focus:ring-green-500' 
                      : usernameStatus === 'taken' 
                      ? 'border-red-300 focus:ring-red-500' 
                      : 'border-neutral-200 focus:ring-primary-500'
                  }`}
                  placeholder="Choose a username (3-30 characters)"
                />
                {usernameStatus === 'checking' && (
                  <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-neutral-400"></div>
                  </div>
                )}
                {usernameStatus === 'available' && (
                  <Check className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-green-500" />
                )}
                {usernameStatus === 'taken' && (
                  <XIcon className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-red-500" />
                )}
              </div>
              <div className="flex items-center justify-between mt-1">
                <p className="text-xs text-neutral-500">
                  Only letters, numbers, hyphens, and underscores allowed
                </p>
                {usernameStatus === 'available' && (
                  <p className="text-xs text-green-600 font-medium">✓ Available</p>
                )}
                {usernameStatus === 'taken' && (
                  <p className="text-xs text-red-600 font-medium">✗ Already taken</p>
                )}
              </div>
            </div>

            <div className="flex space-x-3">
              <button
                type="button"
                onClick={generateNewUsername}
                className="flex-1 bg-neutral-100 text-neutral-700 py-3 px-4 rounded-2xl hover:bg-neutral-200 transition-all duration-200 font-medium"
              >
                Generate New
              </button>
              <button
                type="submit"
                disabled={loading || usernameStatus === 'taken' || usernameStatus === 'checking'}
                className="flex-1 bg-gradient-to-r from-primary-500 to-primary-600 text-white py-3 px-4 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-semibold disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 shadow-colored"
              >
                {loading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Setting up...
                  </div>
                ) : (
                  'Continue'
                )}
              </button>
            </div>
          </form>

          <div className="text-center">
            <p className="text-sm text-neutral-600">
              Your username will be used for your custom URL: <br />
              <span className="font-mono text-primary-600">
                yourdomain.com/@{username || 'username'}
              </span>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
