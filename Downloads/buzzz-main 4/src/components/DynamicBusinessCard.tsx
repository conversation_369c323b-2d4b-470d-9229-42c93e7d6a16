import React, { useState } from 'react';
import { BusinessCard as BusinessCardType, CardComponent, Offer } from '../types';
import { Mail, Phone, Globe, Linkedin, Twitter, Instagram, Facebook, Youtube, MapPin, Calendar, ArrowRight, MessageCircle, Github, Twitch, Music2 as Tik<PERSON>ok, <PERSON><PERSON> as Snap<PERSON>t, Pointer as Pinterest, Disc as Discord } from 'lucide-react';
import { getThemeGradient } from './BusinessCard';

interface DynamicBusinessCardProps {
  card: BusinessCardType;
  components: CardComponent[];
  offers?: any[];
  onOfferClick?: (offer: any) => void;
  isCompact?: boolean;
}

const iconMap = {
  linkedin: Linkedin,
  twitter: Twitter,
  instagram: Instagram,
  facebook: Facebook,
  youtube: Youtube,
  github: Github,
  twitch: Twitch,
  tiktok: TikTok,
  snapchat: Snapchat,
  pinterest: Pinterest,
  discord: Discord,
  email: Mail,
  whatsapp: MessageCircle,
  website: Globe,
};

export default function DynamicBusinessCard({ 
  card, 
  components = [], 
  offers = [],
  onOfferClick,
  isCompact = false 
}: DynamicBusinessCardProps) {
  const [clickedOfferId, setClickedOfferId] = useState<string | null>(null);
  
  const cardClasses = isCompact 
    ? "w-full max-w-sm mx-auto transform transition-all duration-500 ease-in-out hover:scale-105" 
    : "w-full max-w-md mx-auto transform transition-all duration-500 ease-in-out hover:scale-105";

  // Components will be rendered in order below

  // Combine social links with contact info as social buttons
  const allSocialButtons = [
    ...card.socialLinks,
    {
      id: 'email',
      platform: 'Email',
      url: `mailto:${card.email}`,
      icon: 'email'
    },
    {
      id: 'whatsapp',
      platform: 'WhatsApp',
      url: `https://wa.me/${card.phone ? card.phone.replace(/\D/g, '') : ''}`,
      icon: 'whatsapp'
    },
    {
      id: 'website',
      platform: 'Website',
      url: card.website,
      icon: 'website'
    }
  ].filter(link => {
    if (link.icon === 'email') return link.url && link.url !== 'mailto:';
    if (link.icon === 'whatsapp') return card.phone && card.phone !== '+****************';
    if (link.icon === 'website') return link.url && link.url !== 'https://yourwebsite.com';
    return link.url;
  });



  const handleSocialButtonClick = (link: any) => {
    if (link.icon === 'email' || link.icon === 'whatsapp' || link.icon === 'website') {
      // onContactClick?.(link.icon); // We don't have this prop, so skip
    } else {
      // onSocialClick?.(link.platform.toLowerCase()); // We don't have this prop, so skip
    }
  };

  const handleOfferClick = (offer: Offer) => {
    setClickedOfferId(offer.id);
    setTimeout(() => setClickedOfferId(null), 2000);
    onOfferClick?.(offer);
  };

  return (
    <div className={cardClasses}>
      <div className="bg-white rounded-3xl shadow-2xl overflow-hidden border-2 border-gray-100 hover:border-gray-200 transition-all duration-300 hover:shadow-3xl">
        {/* Cover Photo Section */}
        {card.coverImage ? (
          <div className={`${isCompact ? 'h-28' : 'h-36'} relative overflow-hidden`}>
            <img 
              src={card.coverImage} 
              alt="Cover"
              className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>
          </div>
        ) : (
          /* Theme Background when no cover photo */
          <div className={`${isCompact ? 'h-28' : 'h-36'} relative overflow-hidden`}>
            <div className={`w-full h-full bg-gradient-to-r ${getThemeGradient(card.theme)} transition-all duration-500`}></div>
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
            {/* Decorative elements */}
            <div className="absolute top-4 right-4 w-16 h-16 bg-white/10 rounded-full blur-xl"></div>
            <div className="absolute bottom-2 left-4 w-8 h-8 bg-white/20 rounded-full blur-lg"></div>
          </div>
        )}

        {/* Profile Picture - Enhanced positioning and styling */}
        <div className={`relative ${isCompact ? '-mt-12' : '-mt-16'} flex justify-center mb-6`}>
          <div className={`${isCompact ? 'w-20 h-20' : 'w-28 h-28'} rounded-full overflow-hidden border-6 border-white shadow-2xl bg-white ring-4 ring-gray-100 transition-all duration-300 hover:ring-gray-200 hover:shadow-3xl hover:scale-105`}>
            <img 
              src={(() => {
                const profileComponent = components.find(c => c.type === 'profile');
                return profileComponent?.config?.profileImage || card.profileImage;
              })()} 
              alt={card.name}
              className="w-full h-full object-cover transition-transform duration-500 hover:scale-110"
            />
          </div>
        </div>

        {/* Content */}
        <div className={`px-6 sm:px-8 pb-6 sm:pb-8`}>
          {/* Name and Title - Enhanced typography */}
          <div className="text-center mb-6">
            <h1 className={`${isCompact ? 'text-xl sm:text-2xl' : 'text-2xl sm:text-3xl'} font-bold text-gray-900 mb-2 leading-tight tracking-tight`}>
              {card.name}
            </h1>
            <p className={`${isCompact ? 'text-sm sm:text-base' : 'text-base sm:text-lg'} text-gray-600 font-semibold mb-1`}>
              {card.title}
            </p>
            {card.company && (
              <p className={`${isCompact ? 'text-xs sm:text-sm' : 'text-sm sm:text-base'} text-gray-500 font-medium`}>
                {card.company}
              </p>
            )}
          </div>
          {/* Bio - Enhanced spacing and readability */}
          <p className={`text-gray-700 ${isCompact ? 'text-xs sm:text-sm' : 'text-sm sm:text-base'} leading-relaxed mb-6 sm:mb-8 text-center px-2 line-height-loose`}>
            {card.bio}
          </p>
          
          {/* Location and Join Date Style Info - Enhanced styling */}
          <div className={`flex items-center justify-center space-x-6 sm:space-x-8 mb-8 sm:mb-10 ${isCompact ? 'text-xs' : 'text-sm'} text-gray-500`}>
            <div className="flex items-center bg-gray-50 px-3 py-2 rounded-full transition-all duration-200 hover:bg-gray-100">
              <MapPin className={`${isCompact ? 'w-3 h-3' : 'w-4 h-4'} mr-2 text-gray-400`} />
              <span className="font-medium">{card.location || 'Remote'}</span>
            </div>
            <div className="flex items-center bg-gray-50 px-3 py-2 rounded-full transition-all duration-200 hover:bg-gray-100">
              <Calendar className={`${isCompact ? 'w-3 h-3' : 'w-4 h-4'} mr-2 text-gray-400`} />
              <span className="font-medium">Joined {card.joinYear || 2020}</span>
            </div>
          </div>

          {/* All Social Buttons - Enhanced with better hover effects and brand colors */}
          {allSocialButtons.length > 0 && (
            <div className={`flex flex-wrap justify-center gap-3 sm:gap-4 mb-8 sm:mb-10`}>
              {allSocialButtons.map((link) => {
                const IconComponent = iconMap[link.icon as keyof typeof iconMap];
                
                const getHoverStyles = (icon: string) => {
                  const styles: { [key: string]: { bg: string; text: string } } = {
                    linkedin: { bg: '#0077B5', text: '#ffffff' },
                    twitter: { bg: '#1DA1F2', text: '#ffffff' },
                    instagram: { bg: 'linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%)', text: '#ffffff' },
                    facebook: { bg: '#1877F2', text: '#ffffff' },
                    youtube: { bg: '#FF0000', text: '#ffffff' },
                    github: { bg: '#333333', text: '#ffffff' },
                    twitch: { bg: '#9146FF', text: '#ffffff' },
                    tiktok: { bg: '#000000', text: '#ffffff' },
                    snapchat: { bg: '#FFFC00', text: '#000000' },
                    pinterest: { bg: '#BD081C', text: '#ffffff' },
                    discord: { bg: '#5865F2', text: '#ffffff' },
                    email: { bg: '#EA4335', text: '#ffffff' },
                    whatsapp: { bg: '#25D366', text: '#ffffff' },
                    website: { bg: '#4285F4', text: '#ffffff' }
                  };
                  return styles[icon] || { bg: '#4b5563', text: '#ffffff' };
                };
                
                const hoverStyles = getHoverStyles(link.icon);
                
                return IconComponent ? (
                  <a
                    key={link.id}
                    href={link.url}
                    target={link.icon === 'email' ? '_self' : '_blank'}
                    rel={link.icon === 'email' ? undefined : 'noopener noreferrer'}
                    onClick={() => handleSocialButtonClick(link)}
                    className={`${isCompact ? 'w-10 h-10' : 'w-12 h-12 sm:w-14 sm:h-14'} bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl flex items-center justify-center transition-all duration-300 transform hover:scale-110 hover:-translate-y-2 shadow-md hover:shadow-xl social-icon-hover`}
                    style={{
                      '--hover-bg': hoverStyles.bg,
                      '--hover-text': hoverStyles.text,
                    } as React.CSSProperties}
                    title={link.platform}
                  >
                    <IconComponent 
                      className={`${isCompact ? 'w-4 h-4' : 'w-5 h-5 sm:w-6 sm:h-6'} text-gray-600 transition-all duration-300 social-icon`}
                    />
                  </a>
                ) : null;
              })}
            </div>
          )}

          {/* Render all drag-and-drop components in their proper order */}
          {components && components.length > 0 ? (
            // Sort components by their order and render them
            [...components]
              .filter(comp => comp.isActive)
              .sort((a, b) => a.order - b.order)
              .map(component => {
                switch (component.type) {
                  case 'contact':
                    return (
                      <div key={component.id} className="mb-6">
                        <div className="bg-gradient-to-r from-gray-50 via-blue-50 to-gray-50 rounded-2xl p-4 sm:p-6 border border-gray-100 hover:border-gray-200 transition-all duration-300 hover:shadow-lg">
                          <div className="text-center mb-4">
                            <h4 className={`font-bold text-gray-800 mb-3 ${isCompact ? 'text-sm sm:text-base' : 'text-base sm:text-lg'}`}>
                              📞 Contact Information
                            </h4>
                          </div>
                          <div className="space-y-3">
                            {component.config?.showEmail !== false && card.email && (
                              <div className="flex items-center justify-center space-x-3 group">
                                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-200 transition-colors duration-200">
                                  <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                  </svg>
                                </div>
                                <a 
                                  href={`mailto:${card.email}`}
                                  className={`${isCompact ? 'text-xs sm:text-sm' : 'text-sm sm:text-base'} text-gray-700 hover:text-blue-600 transition-colors duration-200 font-medium`}
                                >
                                  {card.email}
                                </a>
                              </div>
                            )}
                            {component.config?.showPhone !== false && card.phone && card.phone !== '+****************' && (
                              <div className="flex items-center justify-center space-x-3 group">
                                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center group-hover:bg-green-200 transition-colors duration-200">
                                  <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                  </svg>
                                </div>
                                <a 
                                  href={`tel:${card.phone}`}
                                  className={`${isCompact ? 'text-xs sm:text-sm' : 'text-sm sm:text-base'} text-gray-700 hover:text-green-600 transition-colors duration-200 font-medium`}
                                >
                                  {card.phone}
                                </a>
                              </div>
                            )}
                            {component.config?.showWebsite !== false && card.website && card.website !== 'https://yourwebsite.com' && (
                              <div className="flex items-center justify-center space-x-3 group">
                                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center group-hover:bg-purple-200 transition-colors duration-200">
                                  <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9" />
                                  </svg>
                                </div>
                                <a 
                                  href={card.website}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className={`${isCompact ? 'text-xs sm:text-sm' : 'text-sm sm:text-base'} text-gray-700 hover:text-purple-600 transition-colors duration-200 font-medium`}
                                >
                                  {card.website ? card.website.replace(/^https?:\/\//, '') : 'yourwebsite.com'}
                                </a>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );

                  case 'link':
                    const linkTitle = component.config?.title || 'Visit Our Website';
                    const linkUrl = component.config?.url || 'https://example.com';
                    const linkDescription = component.config?.description || 'Click to visit our website';
                    
                    return (
                      <div key={component.id} className="mb-6">
                        <a
                          href={linkUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="group cursor-pointer block"
                        >
                          <div className="bg-gradient-to-r from-blue-50 via-purple-50 to-pink-50 rounded-2xl p-4 sm:p-6 hover:from-blue-100 hover:via-purple-100 hover:to-pink-100 transition-all duration-300 border-2 border-transparent hover:border-blue-200 hover:shadow-xl transform hover:scale-105 hover:-translate-y-1">
                            <div className="flex items-center justify-between">
                              <div className="flex-1">
                                <h4 className={`font-bold text-gray-800 mb-2 group-hover:text-blue-600 transition-colors duration-300 ${isCompact ? 'text-sm sm:text-base' : 'text-base sm:text-lg'}`}>
                                  {linkTitle}
                                </h4>
                                <p className={`text-gray-600 mb-3 sm:mb-4 leading-relaxed ${isCompact ? 'text-xs sm:text-sm' : 'text-sm sm:text-base'}`}>
                                  {linkDescription}
                                </p>
                                <div className={`flex items-center text-blue-600 font-bold ${isCompact ? 'text-xs sm:text-sm' : 'text-sm sm:text-base'} group-hover:text-blue-700 transition-colors duration-300`}>
                                  <span className="bg-blue-100 px-3 py-1 rounded-full group-hover:bg-blue-200 transition-colors duration-300 flex items-center">
                                    Visit Link
                                  </span>
                                  <svg className={`${isCompact ? 'w-4 h-4' : 'w-5 h-5'} ml-3 transform group-hover:translate-x-2 transition-transform duration-300`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                  </svg>
                                </div>
                              </div>
                            </div>
                          </div>
                        </a>
                      </div>
                    );

                  case 'offers':
                    const offersTitle = component.config?.title || 'Special Offer';
                    const offersSubtitle = component.config?.subtitle || 'Limited time offer';
                    const offerTitle = component.config?.offerTitle || '20% Off First Order';
                    const offerDescription = component.config?.offerDescription || 'Get 20% off your first purchase with us';
                    const offerButtonText = component.config?.offerButtonText || 'Claim Offer';
                    const offerUrl = component.config?.offerUrl || 'https://example.com/offer';
                    
                    const handleOfferClick = () => {
                      // Check if there's landing page content configured
                      const hasLandingPage = component.config?.landingPageTitle || component.config?.landingPageContent;
                      
                      if (hasLandingPage && onOfferClick) {
                        // Create a mock offer object for the landing page
                        const mockOffer = {
                          id: component.id,
                          title: offerTitle,
                          description: offerDescription,
                          buttonText: offerButtonText,
                          isActive: true,
                          landingPage: {
                            id: component.id + '-landing',
                            title: component.config?.landingPageTitle || offerTitle,
                            subtitle: component.config?.landingPageSubtitle || 'Limited time offer',
                            content: component.config?.landingPageContent || 'Add your offer details here...',
                            ctaText: component.config?.landingPageCtaText || 'Get Started',
                            ctaUrl: component.config?.landingPageCtaUrl || offerUrl,
                                             backgroundColor: component.config?.landingPageBgColor || '#ffffff',
                 textColor: component.config?.landingPageTextColor || '#000000',
                 ctaButtonColor: component.config?.landingPageCtaButtonColor || '#2563eb',
                 ctaButtonTextColor: component.config?.landingPageCtaTextColor || '#ffffff',
                            headerImageUrl: component.config?.landingPageHeaderImage || ''
                          }
                        };
                        onOfferClick(mockOffer);
                      } else if (offerUrl) {
                        window.open(offerUrl, '_blank', 'noopener,noreferrer');
                      }
                    };
                    
                    return (
                      <div key={component.id} className="mb-6">
                        {/* Single offer - NO HEADER SECTION */}
                        <div className="group cursor-pointer" onClick={handleOfferClick}>
                          <div className="bg-gradient-to-r from-blue-50 via-purple-50 to-pink-50 rounded-2xl p-4 sm:p-6 hover:from-blue-100 hover:via-purple-100 hover:to-pink-100 transition-all duration-300 border-2 border-transparent hover:border-blue-200 hover:shadow-xl transform hover:scale-105 hover:-translate-y-1">
                            <div className="flex items-center justify-between">
                              <div className="flex-1">
                                <h4 className={`font-bold text-gray-800 mb-2 group-hover:text-blue-600 transition-colors duration-300 ${isCompact ? 'text-sm sm:text-base' : 'text-base sm:text-lg'}`}>
                                  {offerTitle}
                                </h4>
                                <p className={`text-gray-600 mb-3 sm:mb-4 leading-relaxed ${isCompact ? 'text-xs sm:text-sm' : 'text-sm sm:text-base'}`}>
                                  {offerDescription}
                                </p>
                                <div className={`flex items-center text-blue-600 font-bold ${isCompact ? 'text-xs sm:text-sm' : 'text-sm sm:text-base'} group-hover:text-blue-700 transition-colors duration-300`}>
                                  <span className="bg-blue-100 px-3 py-1 rounded-full group-hover:bg-blue-200 transition-colors duration-300 flex items-center">
                                    {offerButtonText}
                                  </span>
                                  <ArrowRight className={`${isCompact ? 'w-4 h-4' : 'w-5 h-5'} ml-3 transform group-hover:translate-x-2 transition-transform duration-300`} />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    );



                  case 'profile':
                    // Profile is rendered in the header section, so skip here
                    return null;

                  case 'bio':
                    // Bio is rendered in the header section, so skip here  
                    return null;

                  case 'social':
                    // Social links are rendered in the header section, so skip here
                    return null;

                  case 'location':
                    // Location is rendered in the header section, so skip here
                    return null;

                  case 'text':
                    const textTitle = component.config?.title || '';
                    const htmlContent = component.config?.htmlContent || '<p>Add your content here...</p>';
                    const customCss = component.config?.customCss || '';
                    const textAlignment = component.config?.alignment || 'left';
                    const backgroundColor = component.config?.backgroundColor || '#ffffff';
                    const textColor = component.config?.textColor || '#000000';
                    const borderRadius = component.config?.borderRadius || 12;
                    const padding = component.config?.padding || 16;
                    
                    return (
                      <div key={component.id} className="mb-6">
                        {/* Section Title */}
                        {textTitle && (
                          <h3 className={`font-bold text-gray-800 mb-4 text-center ${isCompact ? 'text-lg' : 'text-xl'}`}>
                            {textTitle}
                          </h3>
                        )}
                        
                        <div 
                          className="custom-text overflow-hidden"
                          style={{
                            backgroundColor,
                            color: textColor,
                            borderRadius: `${borderRadius}px`,
                            padding: `${padding}px`,
                            textAlign: textAlignment as any
                          }}
                        >
                          {/* Custom CSS Injection */}
                          <style>{`
                            .custom-text-content {
                              text-align: ${textAlignment} !important;
                            }
                            .custom-text-content .ql-align-center {
                              text-align: center !important;
                            }
                            .custom-text-content .ql-align-right {
                              text-align: right !important;
                            }
                            .custom-text-content .ql-align-left {
                              text-align: left !important;
                            }
                            .custom-text-content .ql-align-justify {
                              text-align: justify !important;
                            }
                            .custom-text-content p:not([class*="ql-align"]), 
                            .custom-text-content div:not([class*="ql-align"]), 
                            .custom-text-content h1:not([class*="ql-align"]), 
                            .custom-text-content h2:not([class*="ql-align"]), 
                            .custom-text-content h3:not([class*="ql-align"]), 
                            .custom-text-content h4:not([class*="ql-align"]), 
                            .custom-text-content h5:not([class*="ql-align"]), 
                            .custom-text-content h6:not([class*="ql-align"]) {
                              text-align: ${textAlignment} !important;
                            }
                            ${customCss || ''}
                          `}</style>
                          
                          {/* HTML Content */}
                          <div 
                            dangerouslySetInnerHTML={{ __html: htmlContent }}
                            className="custom-text-content"
                            style={{
                              textAlign: textAlignment as any
                            }}
                          />
                        </div>
                      </div>
                    );

                  case 'divider':
                    const dividerStyle = component.config?.dividerStyle || 'solid';
                    const dividerColor = component.config?.dividerColor || '#e5e7eb';
                    const dividerThickness = component.config?.dividerThickness || 1;
                    const dividerWidth = component.config?.dividerWidth || 100;
                    const dividerMargin = component.config?.dividerMargin || 30;
                    
                    return (
                      <div 
                        key={component.id} 
                        className="flex justify-center"
                        style={{ margin: `${dividerMargin}px 0` }}
                      >
                        <div
                          style={{
                            width: `${dividerWidth}%`,
                            height: `${dividerThickness}px`,
                            borderStyle: dividerStyle === 'gradient' ? 'none' : dividerStyle,
                            borderTopWidth: dividerStyle === 'gradient' ? 0 : `${dividerThickness}px`,
                            borderTopColor: dividerColor,
                            background: dividerStyle === 'gradient' 
                              ? `linear-gradient(to right, transparent, ${dividerColor}, transparent)` 
                              : 'transparent'
                          }}
                        />
                      </div>
                    );

                  default:
                    return null;
                }
              })
          ) : null}
        </div>
      </div>
    </div>
  );
}