import React from 'react';
import { ShoppingBag } from 'lucide-react';
import EcommerceSettings from '../EcommerceSettings';

interface EcommercePageProps {
  onUpgradeClick?: () => void;
}

export default function EcommercePage({ onUpgradeClick }: EcommercePageProps) {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center">
        <ShoppingBag className="w-6 h-6 text-primary-500 mr-3" />
        <div>
          <h1 className="text-3xl font-bold text-neutral-900">E-commerce</h1>
          <p className="text-neutral-600 mt-1">Configure payment processing and e-commerce features</p>
        </div>
      </div>

      {/* Content */}
      <EcommerceSettings onUpgradeClick={onUpgradeClick} />
    </div>
  );
}