import React, { useState, useEffect } from 'react';
import { Settings, User, Lock, Mail, Calendar, Save, Eye, EyeOff, AlertCircle, Check, Crown } from 'lucide-react';
import { isUsernameReserved } from '../../utils/usernameBlacklist';
import { useAuth } from '../../contexts/AuthContext';
import { supabase } from '../../lib/supabase';

export default function SettingsPage() {
  const { user, userProfile } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState<'account' | 'security'>('account');
  
  // Account settings
  const [email, setEmail] = useState('');
  const [username, setUsername] = useState('');
  const [lastUsernameChange, setLastUsernameChange] = useState<string | null>(null);
  const [canChangeUsername, setCanChangeUsername] = useState(true);
  const [daysUntilUsernameChange, setDaysUntilUsernameChange] = useState(0);
  
  // Password settings
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  // Messages
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  useEffect(() => {
    if (user) {
      loadUserSettings();
    }
  }, [user]);

  const loadUserSettings = async () => {
    try {
      setEmail(user?.email || '');
      
      // Load business card to get username - use limit(1) instead of single()
      const { data: cardData } = await supabase
        .from('business_cards')
        .select('username')
        .eq('user_id', user?.id)
        .limit(1);

      if (cardData && cardData.length > 0) {
        setUsername(cardData[0].username);
      }

      // Load user profile to get last username change date
      const { data: profileData } = await supabase
        .from('user_profiles')
        .select('last_username_change')
        .eq('user_id', user?.id)
        .single();

      if (profileData?.last_username_change) {
        setLastUsernameChange(profileData.last_username_change);
        
        // Calculate if user can change username (100 days since last change)
        const lastChange = new Date(profileData.last_username_change);
        const now = new Date();
        const daysSinceLastChange = Math.floor((now.getTime() - lastChange.getTime()) / (1000 * 60 * 60 * 24));
        
        if (daysSinceLastChange < 100) {
          setCanChangeUsername(false);
          setDaysUntilUsernameChange(100 - daysSinceLastChange);
        }
      }
    } catch (error) {
      console.error('Error loading user settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const showMessage = (type: 'success' | 'error', text: string) => {
    setMessage({ type, text });
    setTimeout(() => setMessage(null), 5000);
  };

  const handleEmailUpdate = async () => {
    if (!email || email === user?.email) {
      showMessage('error', 'Please enter a new email address');
      return;
    }

    setSaving(true);
    try {
      const { error } = await supabase.auth.updateUser({ email });
      
      if (error) {
        showMessage('error', error.message);
      } else {
        showMessage('success', 'Email update initiated. Please check your new email for confirmation.');
      }
    } catch (error) {
      showMessage('error', 'Failed to update email. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handlePasswordUpdate = async () => {
    if (!currentPassword || !newPassword || !confirmPassword) {
      showMessage('error', 'Please fill in all password fields');
      return;
    }

    if (newPassword !== confirmPassword) {
      showMessage('error', 'New passwords do not match');
      return;
    }

    if (newPassword.length < 6) {
      showMessage('error', 'New password must be at least 6 characters long');
      return;
    }

    setSaving(true);
    try {
      // First verify current password by attempting to sign in
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: user?.email || '',
        password: currentPassword
      });

      if (signInError) {
        showMessage('error', 'Current password is incorrect');
        setSaving(false);
        return;
      }

      // Update password
      const { error } = await supabase.auth.updateUser({ password: newPassword });
      
      if (error) {
        showMessage('error', error.message);
      } else {
        showMessage('success', 'Password updated successfully');
        setCurrentPassword('');
        setNewPassword('');
        setConfirmPassword('');
      }
    } catch (error) {
      showMessage('error', 'Failed to update password. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleUsernameUpdate = async () => {
    if (!username || username === '') {
      showMessage('error', 'Please enter a username');
      return;
    }

    if (!canChangeUsername) {
      showMessage('error', `You can change your username again in ${daysUntilUsernameChange} days`);
      return;
    }

    // Validate username format
    const usernameRegex = /^[a-zA-Z0-9-_]+$/;
    if (!usernameRegex.test(username)) {
      showMessage('error', 'Username can only contain letters, numbers, hyphens, and underscores');
      return;
    }

    if (username.length < 3 || username.length > 30) {
      showMessage('error', 'Username must be between 3 and 30 characters');
      return;
    }

    // Check if username is reserved
    if (isUsernameReserved(username)) {
      showMessage('error', 'This username is reserved and cannot be used. Please choose a different one.');
      return;
    }

    setSaving(true);
    try {
      // Check if username is already taken (case-insensitive)
      const { data: existingCardData } = await supabase
        .from('business_cards')
        .select('id, username')
        .filter('username', 'ilike', username)
        .neq('user_id', user?.id)
        .limit(1);

      if (existingCardData && existingCardData.length > 0) {
        showMessage('error', 'Username is already taken (case-insensitive). Please choose another one.');
        setSaving(false);
        return;
      }

      // Update username in business card
      const { error: cardError } = await supabase
        .from('business_cards')
        .update({ 
          username: username,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user?.id);

      if (cardError) {
        // Handle unique constraint violation (409)
        if (cardError.code === '23505' || cardError.message?.toLowerCase().includes('duplicate')) {
          showMessage('error', 'Username is already taken (case-insensitive). Please choose another one.');
        } else {
          showMessage('error', 'Failed to update username. Please try again.');
        }
        setSaving(false);
        return;
      }

      // Update last username change date in user profile
      const { error: profileError } = await supabase
        .from('user_profiles')
        .update({ 
          last_username_change: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user?.id);

      if (profileError) {
        console.error('Error updating profile:', profileError);
      }

      setLastUsernameChange(new Date().toISOString());
      setCanChangeUsername(false);
      setDaysUntilUsernameChange(100);
      
      showMessage('success', 'Username updated successfully! You can change it again in 100 days.');
    } catch (error) {
      showMessage('error', 'Failed to update username. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
        <p className="text-neutral-600">Loading settings...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6 sm:space-y-8">
      {/* Header */}
      <div className="flex items-center">
        <Settings className="w-6 h-6 text-primary-500 mr-3" />
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-neutral-900">Account Settings</h1>
          <p className="text-neutral-600 mt-1">Manage your account preferences and security</p>
        </div>
      </div>

      {/* Message Display */}
      {message && (
        <div className={`p-4 rounded-2xl border flex items-start animate-slide-in ${
          message.type === 'success' 
            ? 'bg-accent-50 border-accent-200 text-accent-800' 
            : 'bg-red-50 border-red-200 text-red-800'
        }`}>
          {message.type === 'success' ? (
            <Check className="w-5 h-5 mr-3 flex-shrink-0 mt-0.5" />
          ) : (
            <AlertCircle className="w-5 h-5 mr-3 flex-shrink-0 mt-0.5" />
          )}
          <p className="font-medium text-sm sm:text-base">{message.text}</p>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-white/70 backdrop-blur-sm rounded-2xl p-1 border border-white/50">
        <button
          onClick={() => setActiveTab('account')}
          className={`flex-1 flex items-center justify-center px-3 sm:px-6 py-3 rounded-xl font-medium transition-all duration-200 ${
            activeTab === 'account'
              ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-colored'
              : 'text-neutral-600 hover:text-neutral-900 hover:bg-white/50'
          }`}
        >
          <User className="w-4 h-4 mr-2" />
          <span className="text-sm sm:text-base">Account</span>
        </button>
        <button
          onClick={() => setActiveTab('security')}
          className={`flex-1 flex items-center justify-center px-3 sm:px-6 py-3 rounded-xl font-medium transition-all duration-200 ${
            activeTab === 'security'
              ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-colored'
              : 'text-neutral-600 hover:text-neutral-900 hover:bg-white/50'
          }`}
        >
          <Lock className="w-4 h-4 mr-2" />
          <span className="text-sm sm:text-base">Security</span>
        </button>
      </div>

      <div className="grid gap-6 lg:grid-cols-2 lg:gap-8">
        {/* Settings Form */}
        <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-soft p-4 sm:p-6 lg:p-8 border border-white/50">
          {activeTab === 'account' ? (
            <div className="space-y-6 sm:space-y-8">
              <div>
                <h2 className="text-lg sm:text-xl font-bold text-neutral-900 mb-4 sm:mb-6 flex items-center">
                  <User className="w-5 h-5 mr-2 text-primary-500" />
                  Account Information
                </h2>

                {/* Email Section */}
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-semibold text-neutral-700 mb-2">
                      Email Address
                    </label>
                    <div className="space-y-3">
                      <input
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="w-full px-4 py-3 border border-neutral-200 rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 text-sm sm:text-base"
                        placeholder="Enter new email address"
                      />
                      <button
                        onClick={handleEmailUpdate}
                        disabled={saving || email === user?.email}
                        className="w-full px-6 py-3 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center text-sm sm:text-base"
                      >
                        <Mail className="w-4 h-4 mr-2" />
                        Update Email
                      </button>
                    </div>
                    <p className="text-xs text-neutral-500 mt-2">
                      You'll need to verify your new email address before the change takes effect.
                    </p>
                  </div>

                  {/* Username Section */}
                  <div>
                    <label className="block text-sm font-semibold text-neutral-700 mb-2">
                      Username
                    </label>
                    <div className="space-y-3">
                      <div className="flex flex-col sm:flex-row">
                        <span className="inline-flex items-center px-3 py-3 sm:py-0 rounded-t-2xl sm:rounded-l-2xl sm:rounded-t-none border border-b-0 sm:border-b sm:border-r-0 border-neutral-200 bg-neutral-50 text-neutral-500 text-sm justify-center sm:justify-start">
                          buzzz.my/@
                        </span>
                        <input
                          type="text"
                          value={username}
                          onChange={(e) => setUsername(e.target.value.toLowerCase().replace(/[^a-zA-Z0-9-_]/g, ''))}
                          disabled={!canChangeUsername}
                          className="flex-1 px-4 py-3 border border-neutral-200 rounded-b-2xl sm:rounded-r-2xl sm:rounded-b-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 disabled:bg-neutral-50 disabled:text-neutral-400 text-sm sm:text-base"
                          placeholder="your-username"
                        />
                      </div>
                      <button
                        onClick={handleUsernameUpdate}
                        disabled={saving || !canChangeUsername || !username}
                        className="w-full px-6 py-3 bg-gradient-to-r from-secondary-500 to-secondary-600 text-white rounded-2xl hover:from-secondary-600 hover:to-secondary-700 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center text-sm sm:text-base"
                      >
                        <User className="w-4 h-4 mr-2" />
                        Update Username
                      </button>
                    </div>
                    
                    {/* Username Change Restriction Info */}
                    <div className="mt-3">
                      {!canChangeUsername ? (
                        <div className="bg-warning-50 border border-warning-200 rounded-lg p-3">
                          <div className="flex items-start">
                            <Calendar className="w-4 h-4 text-warning-600 mr-2 mt-0.5 flex-shrink-0" />
                            <div className="min-w-0 flex-1">
                              <p className="text-warning-800 font-medium text-sm">
                                Username Change Restricted
                              </p>
                              <p className="text-warning-700 text-xs mt-1 break-words">
                                You can change your username again in {daysUntilUsernameChange} days.
                                {lastUsernameChange && (
                                  <span className="block mt-1">
                                    Last changed: {new Date(lastUsernameChange).toLocaleDateString()}
                                  </span>
                                )}
                              </p>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <p className="text-xs text-neutral-500">
                          You can change your username once every 100 days. Choose carefully!
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-6 sm:space-y-8">
              <div>
                <h2 className="text-lg sm:text-xl font-bold text-neutral-900 mb-4 sm:mb-6 flex items-center">
                  <Lock className="w-5 h-5 mr-2 text-primary-500" />
                  Security Settings
                </h2>

                {/* Password Change Section */}
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-semibold text-neutral-700 mb-2">
                      Current Password
                    </label>
                    <div className="relative">
                      <input
                        type={showCurrentPassword ? "text" : "password"}
                        value={currentPassword}
                        onChange={(e) => setCurrentPassword(e.target.value)}
                        className="w-full px-4 py-3 pr-12 border border-neutral-200 rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 text-sm sm:text-base"
                        placeholder="Enter your current password"
                      />
                      <button
                        type="button"
                        onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-neutral-600"
                      >
                        {showCurrentPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-neutral-700 mb-2">
                      New Password
                    </label>
                    <div className="relative">
                      <input
                        type={showNewPassword ? "text" : "password"}
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        className="w-full px-4 py-3 pr-12 border border-neutral-200 rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 text-sm sm:text-base"
                        placeholder="Enter your new password"
                      />
                      <button
                        type="button"
                        onClick={() => setShowNewPassword(!showNewPassword)}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-neutral-600"
                      >
                        {showNewPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-neutral-700 mb-2">
                      Confirm New Password
                    </label>
                    <div className="relative">
                      <input
                        type={showConfirmPassword ? "text" : "password"}
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        className="w-full px-4 py-3 pr-12 border border-neutral-200 rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 text-sm sm:text-base"
                        placeholder="Confirm your new password"
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-neutral-600"
                      >
                        {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                    </div>
                  </div>

                  <button
                    onClick={handlePasswordUpdate}
                    disabled={saving || !currentPassword || !newPassword || !confirmPassword}
                    className="w-full px-6 py-3 bg-gradient-to-r from-accent-500 to-accent-600 text-white rounded-2xl hover:from-accent-600 hover:to-accent-700 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center text-sm sm:text-base"
                  >
                    <Save className="w-4 h-4 mr-2" />
                    {saving ? 'Updating Password...' : 'Update Password'}
                  </button>

                  <div className="bg-neutral-50 border border-neutral-200 rounded-lg p-4">
                    <h4 className="font-medium text-neutral-900 mb-2 text-sm sm:text-base">Password Requirements</h4>
                    <ul className="text-xs sm:text-sm text-neutral-600 space-y-1">
                      <li>• At least 6 characters long</li>
                      <li>• Use a combination of letters, numbers, and symbols</li>
                      <li>• Avoid using personal information</li>
                      <li>• Don't reuse passwords from other accounts</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Account Overview */}
        <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-soft p-4 sm:p-6 lg:p-8 border border-white/50">
          <h2 className="text-lg sm:text-xl font-bold text-neutral-900 mb-4 sm:mb-6">Account Overview</h2>
          
          <div className="space-y-4 sm:space-y-6">
            {/* Current Account Info */}
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 sm:p-4 bg-gradient-to-r from-neutral-50 to-primary-50 rounded-xl border border-neutral-200">
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm font-medium text-neutral-700">Current Email</p>
                  <p className="text-neutral-900 font-semibold text-sm sm:text-base truncate">{user?.email}</p>
                </div>
                <Mail className="w-4 h-4 sm:w-5 sm:h-5 text-primary-500 flex-shrink-0 ml-2" />
              </div>

              <div className="flex items-center justify-between p-3 sm:p-4 bg-gradient-to-r from-neutral-50 to-secondary-50 rounded-xl border border-neutral-200">
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm font-medium text-neutral-700">Current Username</p>
                  <p className="text-neutral-900 font-semibold text-sm sm:text-base truncate">@{username}</p>
                </div>
                <User className="w-4 h-4 sm:w-5 sm:h-5 text-secondary-500 flex-shrink-0 ml-2" />
              </div>

              {userProfile && (
                <div className="flex items-center justify-between p-3 sm:p-4 bg-gradient-to-r from-neutral-50 to-accent-50 rounded-xl border border-neutral-200">
                  <div className="min-w-0 flex-1">
                    <p className="text-xs sm:text-sm font-medium text-neutral-700">Account Type</p>
                    <p className="text-neutral-900 font-semibold text-sm sm:text-base capitalize">{userProfile.userType ? userProfile.userType.replace('_', ' ') : 'N/A'}</p>
                  </div>
                  <Crown className="w-4 h-4 sm:w-5 sm:h-5 text-accent-500 flex-shrink-0 ml-2" />
                </div>
              )}
            </div>

            {/* Security Info */}
            <div className="border-t border-neutral-200 pt-4 sm:pt-6">
              <h3 className="font-semibold text-neutral-900 mb-3 sm:mb-4 text-sm sm:text-base">Security Information</h3>
              <div className="space-y-2 sm:space-y-3 text-xs sm:text-sm">
                <div className="flex justify-between items-start">
                  <span className="text-neutral-600">Account Created</span>
                  <span className="text-neutral-900 font-medium text-right">
                    {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}
                  </span>
                </div>
                <div className="flex justify-between items-start">
                  <span className="text-neutral-600">Last Sign In</span>
                  <span className="text-neutral-900 font-medium text-right">
                    {user?.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleDateString() : 'N/A'}
                  </span>
                </div>
                {lastUsernameChange && (
                  <div className="flex justify-between items-start">
                    <span className="text-neutral-600">Last Username Change</span>
                    <span className="text-neutral-900 font-medium text-right">
                      {new Date(lastUsernameChange).toLocaleDateString()}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Tips */}
            <div className="bg-gradient-to-r from-accent-50 to-primary-50 rounded-xl p-3 sm:p-4 border border-accent-200">
              <h4 className="font-medium text-accent-900 mb-2 text-sm sm:text-base">💡 Security Tips</h4>
              <ul className="text-xs sm:text-sm text-accent-800 space-y-1">
                <li>• Use a strong, unique password</li>
                <li>• Keep your email address up to date</li>
                <li>• Choose a memorable username</li>
                <li>• Sign out from shared devices</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}