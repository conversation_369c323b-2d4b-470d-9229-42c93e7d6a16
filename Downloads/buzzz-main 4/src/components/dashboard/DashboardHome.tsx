import React, { useState, useEffect } from 'react';
import { BarChart3, Eye, Mail, Users, Zap, Edit3, ArrowRight, TrendingUp, Calendar, Globe, Plus, Share2 } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { BusinessCard, Offer } from '../../types';
import { getAnalytics } from '../../utils/analytics';
import BusinessCardComponent from '../BusinessCard';
import DynamicBusinessCard from '../DynamicBusinessCard';

interface DashboardHomeProps {
  businessCard: BusinessCard;
  offers: Offer[];
  onEditCard: () => void;
  onUpgradeClick?: () => void;
}

export default function DashboardHome({ businessCard, offers, onEditCard, onUpgradeClick }: DashboardHomeProps) {
  const { user, hasPermission } = useAuth();
  const [analytics, setAnalytics] = useState({
    totalViews: 0,
    contactClicks: 0,
    offerClicks: 0,
    socialClicks: 0,
    growth: { totalViews: 0, contactClicks: 0, offerClicks: 0, socialClicks: 0 }
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (hasPermission('analytics') && businessCard && user) {
      loadAnalytics();
    } else {
      setLoading(false);
    }
  }, [hasPermission, businessCard, user]);

  const loadAnalytics = async () => {
    if (!businessCard || !user) return;
    
    try {
      const analyticsData = await getAnalytics(user.id, businessCard.id);
      setAnalytics(analyticsData);
    } catch (error) {
      console.error('Error loading analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const activeOffers = offers.filter(offer => offer.isActive);

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-neutral-900 mb-2">
            Welcome back, <span className="text-golden-600">{businessCard.name}</span>! 👋
          </h1>
          <p className="text-neutral-600 text-lg">Here's how your business card is performing</p>
        </div>
        <div className="text-right">
          <p className="text-sm text-neutral-500">Last updated</p>
          <p className="text-sm font-medium text-golden-700">{new Date().toLocaleDateString()}</p>
        </div>
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Left Column: Your Business Card Only */}
        <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-soft p-8 border border-white/50 golden-accent-border">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-neutral-900">Your Business Card</h2>
            <button
              onClick={onEditCard}
              className="flex items-center text-golden-600 hover:text-golden-700 transition-colors font-medium golden-button px-4 py-2 rounded-lg"
            >
              <Edit3 className="w-4 h-4 mr-2" />
              Edit Card
            </button>
          </div>
          
          <div className="bg-gradient-to-br from-neutral-50 via-golden-50 to-primary-50 rounded-2xl p-6 border border-golden-100">
            {businessCard.components && businessCard.components.length > 0 ? (
              <DynamicBusinessCard
                card={businessCard}
                components={businessCard.components}
                offers={activeOffers}
                isCompact={true}
              />
            ) : (
              <BusinessCardComponent 
                card={businessCard} 
                offers={activeOffers}
                isCompact={true}
                offersTitle={businessCard.offersTitle}
                offersSubtitle={businessCard.offersSubtitle}
              />
            )}
          </div>
        </div>

        {/* Right Column: Analytics, Quick Info, Quick Actions */}
        <div className="space-y-6">
          {/* Analytics */}
          {hasPermission('analytics') ? (
            <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-soft p-6 border border-white/50 golden-accent-border">
              <h3 className="text-lg font-bold text-neutral-900 mb-4 flex items-center">
                <BarChart3 className="w-5 h-5 mr-2 text-golden-500" />
                Analytics Overview
              </h3>
              <div className="grid grid-cols-2 gap-4">
                {[
                  { 
                    title: 'Card Views', 
                    value: analytics.totalViews, 
                    growth: analytics.growth.totalViews, 
                    color: 'from-golden-500 to-golden-600', 
                    icon: Eye
                  },
                  { 
                    title: 'Contact Clicks', 
                    value: analytics.contactClicks, 
                    growth: analytics.growth.contactClicks, 
                    color: 'from-primary-500 to-primary-600', 
                    icon: Mail
                  },
                  { 
                    title: 'Offer Clicks', 
                    value: analytics.offerClicks, 
                    growth: analytics.growth.offerClicks, 
                    color: 'from-accent-500 to-accent-600', 
                    icon: Zap
                  },
                  { 
                    title: 'Social Clicks', 
                    value: analytics.socialClicks, 
                    growth: analytics.growth.socialClicks, 
                    color: 'from-secondary-500 to-secondary-600', 
                    icon: Users
                  }
                ].map((stat, index) => (
                  <div key={index} className="text-center">
                    <div className={`w-10 h-10 bg-gradient-to-br ${stat.color} rounded-xl flex items-center justify-center mx-auto mb-2`}>
                      <stat.icon className="w-5 h-5 text-white" />
                    </div>
                    <div className="text-lg font-bold text-neutral-900">{stat.value.toLocaleString()}</div>
                    <div className="text-xs text-neutral-600">{stat.title}</div>
                    <div className={`flex items-center justify-center text-xs font-medium ${stat.growth >= 0 ? 'text-golden-600' : 'text-red-500'}`}>
                      <TrendingUp className={`w-3 h-3 mr-1 ${stat.growth < 0 ? 'rotate-180' : ''}`} />
                      {stat.growth >= 0 ? '+' : ''}{stat.growth}%
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="bg-gradient-to-r from-golden-50 to-primary-50 rounded-2xl p-6 border border-golden-200">
              <div className="text-center">
                <BarChart3 className="w-12 h-12 text-golden-500 mx-auto mb-3" />
                <h3 className="text-lg font-bold text-neutral-900 mb-2">Unlock Analytics</h3>
                <p className="text-neutral-600 mb-4 text-sm">Get detailed insights about your card performance</p>
                <button 
                  onClick={onUpgradeClick}
                  className="golden-button px-4 py-2 rounded-xl font-medium text-sm"
                >
                  Upgrade to Unlimited
                </button>
              </div>
            </div>
          )}

          {/* Quick Info */}
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-soft p-6 border border-white/50 golden-accent-border">
            <h3 className="text-lg font-bold text-neutral-900 mb-4 flex items-center">
              <Globe className="w-5 h-5 mr-2 text-golden-500" />
              Quick Info
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-neutral-600 text-sm">Card Created</span>
                <span className="font-medium text-golden-700 text-sm">
                  {new Date().toLocaleDateString()}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-neutral-600 text-sm">Social Links</span>
                <span className="font-medium text-golden-700 text-sm">{businessCard.socialLinks.length}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-neutral-600 text-sm">Theme</span>
                <span className="font-medium text-golden-700 text-sm capitalize">{businessCard.theme}</span>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-gradient-to-r from-golden-50 to-accent-50 rounded-2xl p-6 border border-golden-200">
            <h3 className="text-lg font-bold text-neutral-900 mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <button 
                onClick={onEditCard}
                className="w-full flex items-center justify-between p-3 bg-white/70 rounded-xl hover:bg-white transition-colors border-l-4 border-golden-400"
              >
                <span className="font-medium text-neutral-900 text-sm">Edit Business Card</span>
                <ArrowRight className="w-4 h-4 text-golden-500" />
              </button>

              <a 
                href="/dashboard/sharing"
                className="w-full flex items-center justify-between p-3 bg-white/70 rounded-xl hover:bg-white transition-colors cursor-pointer border-l-4 border-golden-400"
              >
                <span className="font-medium text-neutral-900 text-sm">Share Your Card</span>
                <Share2 className="w-4 h-4 text-golden-500" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}