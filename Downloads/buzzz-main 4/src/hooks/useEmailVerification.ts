import { useCallback } from 'react'
import { supabase } from '../lib/supabase'

interface EmailVerificationParams {
  email: string
  user_name?: string
}

export const useEmailVerification = () => {
  const sendVerificationEmail = useCallback(async ({ email, user_name }: EmailVerificationParams) => {
    try {
      // Get the current session to get the access token
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session?.access_token) {
        throw new Error('No access token available')
      }

      // Generate a verification link (you can customize this URL)
      const verificationLink = `${window.location.origin}/verify-email?token=${session.access_token}&email=${encodeURIComponent(email)}`
      
      // Call our custom verification email function
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/send-verification-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          email,
          verification_link: verificationLink,
          user_name
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to send verification email')
      }

      const result = await response.json()
      console.log('Verification email sent successfully:', result)
      
      return { success: true, data: result }
    } catch (error) {
      console.error('Error sending verification email:', error)
      return { success: false, error: error.message }
    }
  }, [])

  return { sendVerificationEmail }
}
