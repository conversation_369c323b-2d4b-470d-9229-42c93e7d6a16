export interface BusinessCard {
  id: string;
  userId: string;
  name: string;
  title: string;
  company: string;
  bio: string;
  email: string;
  phone: string;
  website: string;
  profileImage: string;
  backgroundImage?: string;
  coverImage?: string;
  socialLinks: SocialLink[];
  theme: 'default' | 'modern' | 'elegant' | 'sunset' | 'forest' | 'royal' | 'midnight' | 'rose';
  pageBackground?: PageBackground;
  username?: string; // Add username field
  offersTitle?: string; // Add customizable offers title
  offersSubtitle?: string; // Add customizable offers subtitle
  location?: string; // Add location field
  joinYear?: number; // Add join year field
  components?: CardComponent[]; // Add drag-and-drop components
}

export interface SocialLink {
  id: string;
  platform: string;
  url: string;
  icon: string;
}

export interface Offer {
  id: string;
  title: string;
  description: string;
  buttonText: string;
  landingPage: LandingPage;
  isActive: boolean;
}

export interface LandingPage {
  id: string;
  title: string;
  subtitle: string;
  content: string; // Now supports HTML from WYSIWYG editor
  ctaText: string;
  ctaUrl: string;
  backgroundColor: string;
  textColor: string;
  ctaButtonColor?: string;
  ctaButtonTextColor?: string;
  headerImageUrl?: string; // Add this line for user-uploaded header photo
}

export interface PageBackground {
  type: 'gradient' | 'image' | 'pattern';
  value: string;
  overlay?: {
    enabled: boolean;
    color: string;
    opacity: number;
  };
}

export interface SavedCard {
  id: string;
  userId: string;
  savedCardData: BusinessCard;
  savedAt: string;
  notes?: string;
  tags?: string[];
}

export type UserType = 'super_admin' | 'super_unlimited' | 'unlimited_yearly' | 'unlimited_monthly' | 'free' | 'lifetime';

export interface UserProfile {
  id: string;
  userId: string;
  userType: UserType;
  subscriptionStatus: 'active' | 'inactive' | 'cancelled' | 'expired';
  subscriptionStartDate?: string;
  subscriptionEndDate?: string;
  maxOffers: number;
  hasAnalytics: boolean;
  hasEcommerce: boolean;
  canChangeBackground: boolean;
  hasCustomDomains: boolean;
  username?: string;
  lastUsernameChange?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Subscription {
  id: string;
  userId: string;
  planType: 'super_unlimited' | 'unlimited_yearly' | 'unlimited_monthly';
  status: 'active' | 'inactive' | 'cancelled' | 'expired';
  amount: number;
  currency: string;
  billingCycle: 'monthly' | 'yearly' | 'lifetime';
  stripeSubscriptionId?: string;
  stripeCustomerId?: string;
  currentPeriodStart?: string;
  currentPeriodEnd?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserAnalytics {
  id: string;
  userId: string;
  businessCardId: string;
  eventType: 'card_view' | 'contact_click' | 'offer_click' | 'social_click';
  eventData: any;
  ipAddress?: string;
  userAgent?: string;
  referrer?: string;
  createdAt: string;
}

export interface EcommerceSettings {
  id: string;
  userId: string;
  businessCardId: string;
  stripePublishableKey?: string;
  stripeSecretKey?: string;
  paypalClientId?: string;
  paymentMethods: any[];
  taxSettings: any;
  shippingSettings: any;
  createdAt: string;
  updatedAt: string;
}

export interface CustomDomain {
  id: string;
  userId: string;
  businessCardId: string;
  domain: string;
  isVerified: boolean;
  isActive: boolean;
  verificationToken?: string;
  verificationExpiresAt?: string;
  sslCertificateStatus: 'pending' | 'active' | 'failed' | 'expired';
  dnsRecords: any[];
  createdAt: string;
  updatedAt: string;
}

export interface DomainVerification {
  id: string;
  domainId: string;
  verificationType: 'dns' | 'file' | 'meta';
  status: 'pending' | 'success' | 'failed';
  verificationData: any;
  verifiedAt?: string;
  createdAt: string;
}