export interface StripeProduct {
  id: string;
  priceId: string;
  name: string;
  description: string;
  mode: 'subscription' | 'payment';
  price: number;
  currency: string;
  interval?: 'month' | 'year';
}

export const stripeProducts: StripeProduct[] = [
  {
    id: 'prod_SXRApDNDPlhrwQ',
    priceId: 'price_1RcMHkBZkbtRKmT0yUuQbQYd',
    name: 'Unlimited (Monthly)',
    description: 'Unlimited offers, analytics, and custom backgrounds',
    mode: 'subscription',
    price: 9.00,
    currency: 'USD',
    interval: 'month'
  },
  {
    id: 'prod_SXR8d2NAXsweOv',
    priceId: 'price_1RcMFyBZkbtRKmT0HUexJJVs',
    name: 'Unlimited (Yearly)',
    description: 'Unlimited offers, analytics, and custom backgrounds',
    mode: 'subscription',
    price: 99.00,
    currency: 'USD',
    interval: 'year'
  },
  {
    id: 'prod_SXR7vpDLBTfzuW',
    priceId: 'price_1RcMF1BZkbtRKmT0B42SAYsQ',
    name: 'Super Unlimited',
    description: 'Access to all features including e-commerce',
    mode: 'subscription',
    price: 129.00,
    currency: 'USD',
    interval: 'month'
  }
];

export function getProductByPriceId(priceId: string): StripeProduct | undefined {
  return stripeProducts.find(product => product.priceId === priceId);
}

export function getProductsByMode(mode: 'subscription' | 'payment'): StripeProduct[] {
  return stripeProducts.filter(product => product.mode === mode);
}