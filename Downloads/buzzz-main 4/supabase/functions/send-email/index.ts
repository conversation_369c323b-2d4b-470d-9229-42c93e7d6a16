// supabase/functions/send-email/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { Resend } from 'npm:resend'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the request body
    const body = await req.json()

    // Initialize Resend with your API key
    const resend = new Resend(Deno.env.get('RESEND_API_KEY'))

    // Check if this is an admin email (has 'to', 'subject', 'html') or business card offer
    if (body.to && body.subject && body.html) {
      // This is an admin email
      const { to, subject, html } = body

      // Validate required fields
      if (!to || !subject || !html) {
        throw new Error('Missing required fields: to, subject, and html')
      }

      // Send admin email using Resend
      const { data, error } = await resend.emails.send({
        from: '<EMAIL>', // Admin emails from your verified domain
        to: to,
        subject: subject,
        html: html,
      })

      if (error) {
        console.error('Resend error details:', JSON.stringify(error, null, 2))
        console.error('Resend error type:', typeof error)
        console.error('Resend error keys:', Object.keys(error || {}))
        throw new Error(`Failed to send email: ${error.message || error.name || JSON.stringify(error)}`)
      }

      console.log('Admin email sent successfully:', data)

      return new Response(
        JSON.stringify({ 
          success: true, 
          message: "Admin email sent successfully!", 
          data,
          emailId: data?.id 
        }),
        { 
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 200 
        }
      )
    } else {
      // This is a business card offer email
      const { businessCard, offer, recipientEmail, personalMessage } = body

      // Validate required fields
      if (!recipientEmail || !businessCard?.name) {
        throw new Error('Missing required fields: recipientEmail and businessCard.name')
      }

    // Create beautiful HTML email template
    const htmlEmail = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Special Offer from ${businessCard.name}</title>
        <style>
          body {
            font-family: 'Arial', 'Helvetica', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            background: #f4f4f4;
          }
          .email-container {
            background: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          }
          .email-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
          }
          .profile-image {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 4px solid white;
            margin: 0 auto 20px;
            display: block;
          }
          .header-title {
            font-size: 28px;
            font-weight: bold;
            margin: 0 0 10px 0;
          }
          .header-subtitle {
            font-size: 18px;
            opacity: 0.9;
            margin: 0;
          }
          .email-body {
            padding: 40px 30px;
          }
          .greeting {
            font-size: 18px;
            margin-bottom: 20px;
          }
          .offer-section {
            background: #f8f9fa;
            padding: 25px;
            margin: 25px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
          }
          .offer-title {
            font-size: 22px;
            font-weight: bold;
            color: #007bff;
            margin: 0 0 15px 0;
          }
          .offer-description {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
          }
          .cta-button {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            font-size: 16px;
            transition: background 0.3s ease;
          }
          .cta-button:hover {
            background: #0056b3;
          }
          .email-footer {
            background: #f1f3f4;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e1e5e9;
          }
          .contact-info {
            margin-top: 20px;
          }
          .contact-item {
            margin: 8px 0;
            font-size: 14px;
            color: #666;
          }
          .contact-item a {
            color: #007bff;
            text-decoration: none;
          }
          .personal-message {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            font-style: italic;
          }
        </style>
      </head>
      <body>
        <div class="email-container">
          <div class="email-header">
            <img src="${businessCard.profileImage || 'https://via.placeholder.com/80x80'}" alt="${businessCard.name}" class="profile-image">
            <h1 class="header-title">${businessCard.name}</h1>
            <p class="header-subtitle">${businessCard.title || ''}</p>
            <p class="header-subtitle">${businessCard.company || ''}</p>
          </div>
          
          <div class="email-body">
            <p class="greeting">Hi there! 👋</p>
            
            ${personalMessage ? `
              <div class="personal-message">
                <strong>Personal Message:</strong><br>
                ${personalMessage}
              </div>
            ` : ''}
            
            <p>I have a special offer I'd love to share with you!</p>
            
            ${offer ? `
              <div class="offer-section">
                <h2 class="offer-title">${offer.title || 'Special Offer'}</h2>
                <p class="offer-description">${offer.description || 'Check out this amazing offer!'}</p>
                ${offer.landingPage?.ctaUrl ? `
                  <a href="${offer.landingPage.ctaUrl}" class="cta-button">
                    ${offer.landingPage.ctaText || 'Learn More'}
                  </a>
                ` : ''}
              </div>
            ` : ''}
            
            <p>Looking forward to connecting with you!</p>
          </div>
          
          <div class="email-footer">
            <p style="font-size: 18px; font-weight: bold; margin: 0 0 10px 0;">
              Best regards,<br>
              <span style="color: #007bff;">${businessCard.name}</span>
            </p>
            
            <div class="contact-info">
              ${businessCard.email ? `
                <div class="contact-item">
                  �� <a href="mailto:${businessCard.email}">${businessCard.email}</a>
                </div>
              ` : ''}
              ${businessCard.phone ? `
                <div class="contact-item">
                  📱 ${businessCard.phone}
                </div>
              ` : ''}
              ${businessCard.website ? `
                <div class="contact-item">
                  🌐 <a href="${businessCard.website}">${businessCard.website}</a>
                </div>
              ` : ''}
            </div>
          </div>
        </div>
      </body>
      </html>
    `

    // Send business card offer email using Resend
    const { data, error } = await resend.emails.send({
      from: '<EMAIL>', // Business card offers from offers domain
      to: recipientEmail,
      subject: `Special Offer from ${businessCard.name}`,
      html: htmlEmail,
    })

          if (error) {
        console.error('Resend error details:', JSON.stringify(error, null, 2))
        console.error('Resend error type:', typeof error)
        console.error('Resend error keys:', Object.keys(error || {}))
        throw new Error(`Failed to send email: ${error.message || error.name || JSON.stringify(error)}`)
      }

    console.log('Business card offer email sent successfully:', data)

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: "Business card offer email sent successfully!", 
        data,
        emailId: data?.id 
      }),
      { 
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200 
      }
    )
  }

  } catch (error) {
    console.error('Function error:', error)
    return new Response(
      JSON.stringify({ 
        error: error.message,
        details: error.stack 
      }),
      { 
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 400 
      }
    )
  }
})