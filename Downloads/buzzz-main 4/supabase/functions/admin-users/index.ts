import { createClient } from 'npm:@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',
};

interface Database {
  public: {
    Tables: {
      user_profiles: {
        Row: {
          id: string;
          user_id: string;
          user_type: string;
          subscription_status: string;
          subscription_start_date?: string;
          subscription_end_date?: string;
          max_offers: number;
          has_analytics: boolean;
          has_ecommerce: boolean;
          can_change_background: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          user_id: string;
          user_type?: string;
          subscription_status?: string;
          subscription_start_date?: string;
          subscription_end_date?: string;
          max_offers?: number;
          has_analytics?: boolean;
          has_ecommerce?: boolean;
          can_change_background?: boolean;
        };
        Update: {
          user_type?: string;
          subscription_status?: string;
          subscription_start_date?: string;
          subscription_end_date?: string;
          max_offers?: number;
          has_analytics?: boolean;
          has_ecommerce?: boolean;
          can_change_background?: boolean;
          updated_at?: string;
        };
      };
    };
  };
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Create Supabase client with service role key for admin operations
    const supabaseAdmin = createClient<Database>(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Create regular client to verify user permissions
    const supabase = createClient<Database>(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        global: {
          headers: {
            Authorization: authHeader,
          },
        },
      }
    );

    // Verify the user is authenticated and has admin permissions
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Check if user has admin permissions
    const { data: userProfile, error: profileError } = await supabase
      .from('user_profiles')
      .select('user_type')
      .eq('user_id', user.id)
      .single();

    if (profileError || !userProfile || userProfile.user_type !== 'super_admin') {
      return new Response(
        JSON.stringify({ error: 'Insufficient permissions' }),
        { 
          status: 403, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    if (req.method === 'GET') {
      // Get all users with their profiles using admin client
      const { data: users, error: usersError } = await supabaseAdmin.auth.admin.listUsers();
      
      if (usersError) {
        throw usersError;
      }

      // Get user profiles
      const { data: profiles, error: profilesError } = await supabaseAdmin
        .from('user_profiles')
        .select('*');

      if (profilesError) {
        throw profilesError;
      }

      // Combine user data with profiles
      const usersWithProfiles = users.users.map(user => {
        const profile = profiles?.find(p => p.user_id === user.id);
        return {
          id: user.id,
          email: user.email,
          created_at: user.created_at,
          profile: profile ? {
            id: profile.id,
            userId: profile.user_id,
            userType: profile.user_type,
            subscriptionStatus: profile.subscription_status,
            subscriptionStartDate: profile.subscription_start_date,
            subscriptionEndDate: profile.subscription_end_date,
            maxOffers: profile.max_offers,
            hasAnalytics: profile.has_analytics,
            hasEcommerce: profile.has_ecommerce,
            canChangeBackground: profile.can_change_background,
            createdAt: profile.created_at,
            updatedAt: profile.updated_at
          } : null
        };
      });

      return new Response(
        JSON.stringify({ users: usersWithProfiles }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    if (req.method === 'PUT') {
      const { userId, userType, subscriptionEndDate, subscriptionStatus } = await req.json();

      if (!userId || !userType) {
        return new Response(
          JSON.stringify({ error: 'Missing userId or userType' }),
          { 
            status: 400, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        );
      }

      // Calculate subscription end date if not provided
      let endDate = subscriptionEndDate;
      if (!endDate && userType !== 'free') {
        // Default to 30 days from now for premium plans
        const defaultEndDate = new Date();
        defaultEndDate.setDate(defaultEndDate.getDate() + 30);
        endDate = defaultEndDate.toISOString();
      }

      // Set subscription start date to now for new premium plans
      const startDate = userType !== 'free' ? new Date().toISOString() : null;

      // Set subscription status based on user type
      let status = subscriptionStatus;
      if (!status) {
        status = userType === 'free' ? 'expired' : 'active';
      }

      // Update user profile using admin client
      const { error: updateError } = await supabaseAdmin
        .from('user_profiles')
        .update({
          user_type: userType,
          subscription_status: status,
          subscription_start_date: startDate,
          subscription_end_date: endDate,
          max_offers: userType === 'free' ? 3 : -1,
          has_analytics: userType !== 'free',
          has_ecommerce: userType === 'super_unlimited',
          can_change_background: userType !== 'free',
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (updateError) {
        throw updateError;
      }

      return new Response(
        JSON.stringify({ success: true }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    if (req.method === 'DELETE') {
      const { userId } = await req.json();

      if (!userId) {
        return new Response(
          JSON.stringify({ error: 'Missing userId' }),
          { 
            status: 400, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        );
      }

      // Check if trying to delete self
      if (userId === user.id) {
        return new Response(
          JSON.stringify({ error: 'Cannot delete your own account' }),
          { 
            status: 400, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        );
      }

      try {
        console.log('Starting user deletion process for:', userId);
        
        // First, check if user exists and get their data
        const { data: userData, error: userCheckError } = await supabaseAdmin.auth.admin.getUserById(userId);
        if (userCheckError) {
          console.error('Error checking user existence:', userCheckError);
          throw new Error(`User not found: ${userCheckError.message}`);
        }
        
        console.log('User found:', userData.user?.email);

        // Check for related data that might prevent deletion
        const { data: businessCards, error: cardsError } = await supabaseAdmin
          .from('business_cards')
          .select('id')
          .eq('user_id', userId);
        
        if (cardsError) {
          console.error('Error checking business cards:', cardsError);
        } else {
          console.log('Found business cards:', businessCards?.length || 0);
        }

        const { data: subscriptions, error: subsError } = await supabaseAdmin
          .from('stripe_subscriptions')
          .select('id')
          .eq('user_id', userId);
        
        if (subsError) {
          console.error('Error checking subscriptions:', subsError);
        } else {
          console.log('Found subscriptions:', subscriptions?.length || 0);
        }

        // Check for stripe_customers entry
        const { data: stripeCustomer, error: customerError } = await supabaseAdmin
          .from('stripe_customers')
          .select('id, customer_id')
          .eq('user_id', userId);
        
        if (customerError) {
          console.error('Error checking stripe_customers:', customerError);
        } else {
          console.log('Found stripe customer:', stripeCustomer?.length || 0);
        }

        // Check for stripe_orders
        let stripeOrders: any[] = [];
        if (stripeCustomer && stripeCustomer.length > 0) {
          const { data: orders, error: ordersError } = await supabaseAdmin
            .from('stripe_orders')
            .select('id')
            .eq('customer_id', stripeCustomer[0].customer_id);
          
          if (ordersError) {
            console.error('Error checking stripe_orders:', ordersError);
          } else {
            stripeOrders = orders || [];
            console.log('Found stripe orders:', stripeOrders.length);
          }
        }

        // Delete related data first (in reverse dependency order)
        console.log('Deleting related data...');
        
        // Delete stripe_orders first (they reference stripe_customers)
        if (stripeOrders.length > 0) {
          const { error: ordersDeleteError } = await supabaseAdmin
            .from('stripe_orders')
            .delete()
            .in('id', stripeOrders.map(o => o.id));
          
          if (ordersDeleteError) {
            console.error('Error deleting stripe orders:', ordersDeleteError);
            throw new Error(`Failed to delete stripe orders: ${ordersDeleteError.message}`);
          }
          console.log('Stripe orders deleted successfully');
        }

        // Delete stripe_subscriptions (they reference stripe_customers)
        if (subscriptions && subscriptions.length > 0) {
          const { error: subsDeleteError } = await supabaseAdmin
            .from('stripe_subscriptions')
            .delete()
            .eq('user_id', userId);
          
          if (subsDeleteError) {
            console.error('Error deleting subscriptions:', subsDeleteError);
            throw new Error(`Failed to delete subscriptions: ${subsDeleteError.message}`);
          }
          console.log('Subscriptions deleted successfully');
        }

        // Delete stripe_customers entry
        if (stripeCustomer && stripeCustomer.length > 0) {
          const { error: customerDeleteError } = await supabaseAdmin
            .from('stripe_customers')
            .delete()
            .eq('user_id', userId);
          
          if (customerDeleteError) {
            console.error('Error deleting stripe customer:', customerDeleteError);
            throw new Error(`Failed to delete stripe customer: ${customerDeleteError.message}`);
          }
          console.log('Stripe customer deleted successfully');
        }
        
        // Delete business cards
        if (businessCards && businessCards.length > 0) {
          const { error: cardsDeleteError } = await supabaseAdmin
            .from('business_cards')
            .delete()
            .eq('user_id', userId);
          
          if (cardsDeleteError) {
            console.error('Error deleting business cards:', cardsDeleteError);
            throw new Error(`Failed to delete business cards: ${cardsDeleteError.message}`);
          }
          console.log('Business cards deleted successfully');
        }

        // Delete user profile
        console.log('Deleting user profile...');
        const { error: profileDeleteError } = await supabaseAdmin
          .from('user_profiles')
          .delete()
          .eq('user_id', userId);

        if (profileDeleteError) {
          console.error('Error deleting user profile:', profileDeleteError);
          throw new Error(`Failed to delete user profile: ${profileDeleteError.message}`);
        }
        console.log('User profile deleted successfully');

        // Finally, delete user from auth.users
        console.log('Deleting user from auth system...');
        const { error: authDeleteError } = await supabaseAdmin.auth.admin.deleteUser(userId);

        if (authDeleteError) {
          console.error('Error deleting user from auth:', authDeleteError);
          throw new Error(`Failed to delete user from auth system: ${authDeleteError.message}`);
        }
        console.log('User deleted from auth system successfully');

        return new Response(
          JSON.stringify({ success: true, message: 'User deleted successfully' }),
          { 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        );
      } catch (error) {
        console.error('Error deleting user:', error);
        return new Response(
          JSON.stringify({ error: `Failed to delete user: ${error.message}` }),
          { 
            status: 500, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        );
      }
    }

    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        status: 405, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('Error in admin-users function:', error);
    return new Response(
      JSON.stringify({ error: error.message || 'Internal server error' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});