import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create a Supabase client with the Auth context of the function
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    // Get the user from the request
    const { data: { user }, error: userError } = await supabaseClient.auth.getUser()
    
    if (userError || !user) {
      throw new Error('User not authenticated')
    }

    // Check if user is super_admin
    const { data: userProfile, error: profileError } = await supabaseClient
      .from('user_profiles')
      .select('user_type')
      .eq('user_id', user.id)
      .single()

    if (profileError || userProfile?.user_type !== 'super_admin') {
      throw new Error('Access denied: Super admin required')
    }

    // Get all users from auth.users
    const { data: authUsers, error: authError } = await supabaseClient.auth.admin.listUsers()
    
    if (authError) {
      throw new Error(`Error listing users: ${authError.message}`)
    }

    // Get existing user profiles
    const { data: existingProfiles, error: profilesError } = await supabaseClient
      .from('user_profiles')
      .select('user_id')
    
    if (profilesError) {
      throw new Error(`Error getting profiles: ${profilesError.message}`)
    }

    const existingUserIds = new Set(existingProfiles?.map(p => p.user_id) || [])
    const missingUsers = authUsers.users.filter(u => !existingUserIds.has(u.id))

    if (missingUsers.length === 0) {
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: 'All users already have profiles',
          synced_count: 0,
          total_users: authUsers.users.length
        }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200 
        }
      )
    }

    // Create profiles for missing users
    const profilesToInsert = missingUsers.map(u => ({
      user_id: u.id,
      user_type: 'user',
      subscription_status: 'free',
      has_analytics: false,
      has_custom_domains: false,
      has_ecommerce: false,
      can_change_background: true,
      max_offers: 3,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }))

    const { data: insertedProfiles, error: insertError } = await supabaseClient
      .from('user_profiles')
      .insert(profilesToInsert)
      .select()

    if (insertError) {
      throw new Error(`Error creating profiles: ${insertError.message}`)
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: `Successfully synced ${insertedProfiles.length} user profiles`,
        synced_count: insertedProfiles.length,
        total_users: authUsers.users.length,
        synced_users: insertedProfiles.map(p => p.user_id)
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    )

  } catch (error) {
    console.error('Error in sync-user-profiles function:', error)
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400 
      }
    )
  }
})
