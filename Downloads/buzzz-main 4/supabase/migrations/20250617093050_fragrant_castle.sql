/*
  # Add INSERT policy for user profiles

  1. Security Changes
    - Add policy for authenticated users to insert their own profile during signup
    - This allows the signup process to create user profiles without R<PERSON> blocking the operation

  2. Policy Details
    - Policy name: "Users can insert own profile"
    - Allows INSERT operations for authenticated users
    - Restricts users to only insert profiles where user_id matches their auth.uid()
*/

-- Add INSERT policy for user_profiles table
CREATE POLICY "Users can insert own profile"
  ON user_profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);