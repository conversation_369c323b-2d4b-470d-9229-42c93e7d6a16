/*
  # Fix Existing Lifetime Users with Wrong Dates

  This migration fixes any existing lifetime users who have incorrect expiry dates
  and ensures they have permanent access.

  Problem: Some lifetime users have past dates instead of permanent future dates.
  Solution: Update all lifetime users to have '2099-12-31' as their end date.
*/

-- Fix all existing lifetime users to have permanent dates
UPDATE user_profiles 
SET 
  subscription_end_date = '2099-12-31'::date,
  subscription_status = 'active',
  updated_at = now()
WHERE user_type = 'lifetime' 
  AND (subscription_end_date IS NULL OR subscription_end_date < CURRENT_DATE);

-- Log the fix for existing lifetime users
INSERT INTO user_profiles_history (
  user_id,
  user_type,
  subscription_status,
  max_offers,
  has_analytics,
  has_ecommerce,
  can_change_background,
  change_reason
)
SELECT 
  up.user_id,
  up.user_type,
  up.subscription_status,
  up.max_offers,
  up.has_analytics,
  up.has_ecommerce,
  up.can_change_background,
  'Fixed existing lifetime user - corrected expiry date to permanent (2099-12-31)'
FROM user_profiles up
WHERE up.user_type = 'lifetime' 
  AND up.subscription_end_date = '2099-12-31'::date
  AND NOT EXISTS (
    SELECT 1 FROM user_profiles_history uph 
    WHERE uph.user_id = up.user_id 
      AND uph.change_reason LIKE '%Fixed existing lifetime user%'
  );

-- Verify the fix
DO $$
DECLARE
  fixed_count integer;
  total_lifetime integer;
BEGIN
  SELECT COUNT(*) INTO total_lifetime
  FROM user_profiles 
  WHERE user_type = 'lifetime';
  
  SELECT COUNT(*) INTO fixed_count
  FROM user_profiles 
  WHERE user_type = 'lifetime' 
    AND subscription_end_date = '2099-12-31'::date;
  
  RAISE NOTICE 'Total lifetime users: %, Users with permanent dates: %', total_lifetime, fixed_count;
  
  IF fixed_count = total_lifetime THEN
    RAISE NOTICE '✅ All lifetime users now have permanent access!';
  ELSE
    RAISE NOTICE '⚠️ Some lifetime users still need fixing';
  END IF;
END $$;
