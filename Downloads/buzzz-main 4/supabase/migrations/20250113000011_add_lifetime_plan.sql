-- Add Lifetime Plan - Pay once, access forever
-- Lifetime users get permanent access like super admins but are regular users

-- First, let's add the lifetime plan type and update existing functions
-- to handle lifetime users the same as super admins (never expire)

-- Update the subscription check functions to exclude lifetime users
DROP FUNCTION IF EXISTS get_users_expiring_soon(integer);

CREATE OR REPLACE FUNCTION get_users_expiring_soon(days_ahead integer DEFAULT 3)
RETURNS TABLE(
  user_id uuid,
  email character varying(255),
  user_type text,
  days_until_expiry integer
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    up.user_id,
    au.email,
    up.user_type,
    (up.subscription_end_date - CURRENT_DATE) as days_until_expiry
  FROM user_profiles up
  JOIN auth.users au ON up.user_id = au.id
  WHERE up.subscription_end_date IS NOT NULL
    AND up.subscription_end_date <= CURRENT_DATE + (days_ahead || ' days')::interval
    AND up.user_type NOT IN ('super_admin', 'lifetime') -- Exclude super admins and lifetime users
    AND up.subscription_end_date < '2099-01-01'::date -- Exclude far future dates
  ORDER BY up.subscription_end_date ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the downgrade function to exclude lifetime users
DROP FUNCTION IF EXISTS check_and_downgrade_subscriptions();

CREATE OR REPLACE FUNCTION check_and_downgrade_subscriptions()
RETURNS text AS $$
DECLARE
  downgraded_count integer := 0;
  user_record record;
BEGIN
  -- Find users with expired subscriptions (excluding super admins and lifetime users)
  FOR user_record IN
    SELECT up.user_id, up.user_type
    FROM user_profiles up
    WHERE up.subscription_end_date IS NOT NULL
      AND up.subscription_end_date < CURRENT_DATE
      AND up.user_type NOT IN ('super_admin', 'lifetime') -- Exclude super admins and lifetime users
      AND up.subscription_end_date < '2099-01-01'::date -- Exclude far future dates
      AND up.subscription_status != 'inactive' -- Don't process already downgraded
  LOOP
    -- Downgrade to free plan
    UPDATE user_profiles 
    SET 
      user_type = 'free',
      subscription_status = 'inactive',
      max_offers = 3,
      has_analytics = false,
      has_ecommerce = false,
      can_change_background = false
    WHERE user_id = user_record.user_id;
    
    -- Log the change
    INSERT INTO user_profiles_history (
      user_id,
      user_type,
      subscription_status,
      max_offers,
      has_analytics,
      has_ecommerce,
      can_change_background,
      change_reason
    ) VALUES (
      user_record.user_id,
      'free',
      'inactive',
      3,
      false,
      false,
      false,
      'Automatic downgrade: ' || user_record.user_type || ' subscription expired'
    );
    
    downgraded_count := downgraded_count + 1;
  END LOOP;
  
  RETURN format('Downgraded %d expired subscriptions (super admins and lifetime users excluded)', downgraded_count);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the get all users function to show lifetime status
DROP FUNCTION IF EXISTS get_all_users_subscription_status();

CREATE OR REPLACE FUNCTION get_all_users_subscription_status()
RETURNS TABLE(
  user_id uuid,
  email text,
  user_type text,
  subscription_status text,
  subscription_start_date date,
  subscription_end_date date,
  days_until_expiry integer,
  is_expired boolean,
  is_super_admin boolean,
  is_lifetime boolean
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    up.user_id,
    au.email::text,
    up.user_type,
    up.subscription_status,
    up.subscription_start_date::date,
    up.subscription_end_date::date,
    CASE 
      WHEN up.user_type IN ('super_admin', 'lifetime') THEN NULL -- Super admins and lifetime users don't expire
      WHEN up.subscription_end_date IS NULL THEN NULL
      ELSE (up.subscription_end_date::date - CURRENT_DATE)
    END as days_until_expiry,
    CASE 
      WHEN up.user_type IN ('super_admin', 'lifetime') THEN false -- Super admins and lifetime users never expire
      WHEN up.subscription_end_date IS NULL THEN false
      ELSE up.subscription_end_date::date <= CURRENT_DATE
    END as is_expired,
    (up.user_type = 'super_admin') as is_super_admin,
    (up.user_type = 'lifetime') as is_lifetime
  FROM user_profiles up
  JOIN auth.users au ON up.user_id = au.id
  ORDER BY 
    up.user_type = 'super_admin' DESC, -- Super admins first
    up.user_type = 'lifetime' DESC, -- Lifetime users second
    CASE WHEN up.subscription_end_date IS NULL THEN 1 ELSE 0 END,
    up.subscription_end_date ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users (admins)
GRANT EXECUTE ON FUNCTION get_all_users_subscription_status() TO authenticated;

-- Create a function to upgrade a user to lifetime plan
CREATE OR REPLACE FUNCTION upgrade_user_to_lifetime(user_id_param uuid)
RETURNS text AS $$
DECLARE
  user_email text;
BEGIN
  -- Get user email for logging
  SELECT au.email INTO user_email
  FROM auth.users au
  WHERE au.id = user_id_param;
  
  IF NOT FOUND THEN
    RETURN 'User not found';
  END IF;
  
  -- Upgrade user to lifetime plan
  UPDATE user_profiles 
  SET 
    user_type = 'lifetime',
    subscription_status = 'active',
    subscription_start_date = CURRENT_DATE,
    subscription_end_date = '2099-12-31'::date, -- Far future date (effectively permanent)
    max_offers = -1, -- Unlimited offers
    has_analytics = true,
    has_ecommerce = true,
    can_change_background = true
  WHERE user_id = user_id_param;
  
  -- Log the change
  INSERT INTO user_profiles_history (
    user_id,
    user_type,
    subscription_status,
    max_offers,
    has_analytics,
    has_ecommerce,
    can_change_background,
    change_reason
  ) VALUES (
    user_id_param,
    'lifetime',
    'active',
    -1,
    true,
    true,
    true,
    'Upgraded to lifetime plan - permanent access granted'
  );
  
  RETURN format('User %s upgraded to lifetime plan successfully', user_email);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users (admins)
GRANT EXECUTE ON FUNCTION upgrade_user_to_lifetime(uuid) TO authenticated;

-- Create a function to check if a user has permanent access (super admin or lifetime)
CREATE OR REPLACE FUNCTION has_permanent_access(user_id_param uuid)
RETURNS boolean AS $$
BEGIN
  RETURN EXISTS(
    SELECT 1 FROM user_profiles 
    WHERE user_id = user_id_param 
      AND user_type IN ('super_admin', 'lifetime')
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION has_permanent_access(uuid) TO authenticated;

-- Add comments explaining the lifetime plan system
COMMENT ON FUNCTION upgrade_user_to_lifetime(uuid) IS 
'Upgrade a user to lifetime plan. Lifetime users get permanent access with all premium features.';

COMMENT ON FUNCTION has_permanent_access(uuid) IS 
'Check if a user has permanent access (super admin or lifetime plan). These users never expire.';
