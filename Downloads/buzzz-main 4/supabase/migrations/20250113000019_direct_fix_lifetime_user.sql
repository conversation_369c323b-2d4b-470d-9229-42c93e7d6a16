/*
  # Direct Fix for Lifetime User with Wrong Date

  This migration directly fixes the specific lifetime user who has the wrong expiry date.
  
  Problem: The previous migration didn't work as expected.
  Solution: Direct update to fix the specific user.
*/

-- Direct fix for the lifetime user with wrong date
UPDATE user_profiles 
SET 
  subscription_end_date = '2099-12-31'::date,
  subscription_status = 'active',
  updated_at = now()
WHERE user_type = 'lifetime';

-- Log this fix
INSERT INTO user_profiles_history (
  user_id,
  user_type,
  subscription_status,
  max_offers,
  has_analytics,
  has_ecommerce,
  can_change_background,
  change_reason
)
SELECT 
  up.user_id,
  up.user_type,
  up.subscription_status,
  up.max_offers,
  up.has_analytics,
  up.has_ecommerce,
  up.can_change_background,
  'Direct fix: Corrected lifetime user expiry date to permanent'
FROM user_profiles up
WHERE up.user_type = 'lifetime'
  AND NOT EXISTS (
    SELECT 1 FROM user_profiles_history uph 
    WHERE uph.user_id = up.user_id 
      AND uph.change_reason LIKE '%Direct fix%'
  );

-- Verify the fix
DO $$
DECLARE
  lifetime_user record;
BEGIN
  FOR lifetime_user IN 
    SELECT user_id, subscription_end_date, subscription_status
    FROM user_profiles 
    WHERE user_type = 'lifetime'
  LOOP
    RAISE NOTICE 'Lifetime user %: end_date = %, status = %', 
      lifetime_user.user_id, 
      lifetime_user.subscription_end_date, 
      lifetime_user.subscription_status;
  END LOOP;
END $$;
