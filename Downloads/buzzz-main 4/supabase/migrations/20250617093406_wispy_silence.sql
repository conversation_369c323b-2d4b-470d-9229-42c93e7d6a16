/*
  # Fix user signup database error

  1. Issues Fixed
    - Ensure the create_user_profile trigger function works correctly
    - Fix any RLS policy issues that might prevent user profile creation
    - Add proper error handling to the trigger function

  2. Changes Made
    - Recreate the create_user_profile function with proper error handling
    - Ensure the trigger is properly set up on auth.users
    - Add a policy to allow the trigger to insert user profiles
*/

-- Drop existing function if it exists to recreate it properly
DROP FUNCTION IF EXISTS create_user_profile() CASCADE;

-- Create the user profile creation function
CREATE OR REPLACE FUNCTION create_user_profile()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_profiles (
    user_id,
    user_type,
    subscription_status,
    max_offers,
    has_analytics,
    has_ecommerce,
    can_change_background,
    created_at,
    updated_at
  )
  VALUES (
    NEW.id,
    'free',
    'active',
    3,
    false,
    false,
    false,
    NOW(),
    NOW()
  );
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail the user creation
    RAISE LOG 'Error creating user profile for user %: %', NEW.id, SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the trigger on auth.users table
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION create_user_profile();

-- Add a policy to allow the system to insert user profiles during signup
DROP POLICY IF EXISTS "System can insert user profiles" ON user_profiles;
CREATE POLICY "System can insert user profiles"
  ON user_profiles
  FOR INSERT
  TO authenticated, anon
  WITH CHECK (true);

-- Ensure the service role can manage user profiles
DROP POLICY IF EXISTS "Service role can manage user profiles" ON user_profiles;
CREATE POLICY "Service role can manage user profiles"
  ON user_profiles
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);