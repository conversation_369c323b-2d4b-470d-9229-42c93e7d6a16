/*
  # Create offers table

  1. New Tables
    - `offers`
      - `id` (uuid, primary key)
      - `user_id` (uuid, foreign key to auth.users)
      - `business_card_id` (uuid, foreign key to business_cards)
      - `title` (text, offer title)
      - `description` (text, offer description)
      - `button_text` (text, CTA button text)
      - `landing_page` (jsonb, landing page configuration)
      - `is_active` (boolean, whether offer is active)
      - `created_at` (timestamptz, creation timestamp)
      - `updated_at` (timestamptz, last update timestamp)

  2. Security
    - Enable RLS on `offers` table
    - Add policy for users to manage their own offers
    - Add policy for public read access to active offers
*/

CREATE TABLE IF NOT EXISTS offers (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  business_card_id uuid REFERENCES business_cards(id) ON DELETE CASCADE NOT NULL,
  title text NOT NULL DEFAULT '',
  description text NOT NULL DEFAULT '',
  button_text text NOT NULL DEFAULT 'Learn More',
  landing_page jsonb DEFAULT '{}'::jsonb,
  is_active boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE offers ENABLE ROW LEVEL SECURITY;

-- Policy for users to manage their own offers
CREATE POLICY "Users can manage own offers"
  ON offers
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Policy for public read access to active offers
CREATE POLICY "Public can view active offers"
  ON offers
  FOR SELECT
  TO anon, authenticated
  USING (is_active = true);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_offers_user_id ON offers(user_id);
CREATE INDEX IF NOT EXISTS idx_offers_business_card_id ON offers(business_card_id);
CREATE INDEX IF NOT EXISTS idx_offers_is_active ON offers(is_active);