/*
  # Add offers_title column to business_cards table

  1. Changes
    - Add `offers_title` column to `business_cards` table
    - Set default value to 'Special Offers'
    - Column is nullable to allow for existing records

  2. Notes
    - This resolves the PGRST204 error where the application expects an offers_title column
    - Existing business cards will get the default value
*/

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'business_cards' AND column_name = 'offers_title'
  ) THEN
    ALTER TABLE business_cards ADD COLUMN offers_title text DEFAULT 'Special Offers';
  END IF;
END $$;