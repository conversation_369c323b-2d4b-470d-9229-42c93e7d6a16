/*
  # Create User Profiles History Table

  This migration creates the user_profiles_history table that tracks changes
  to user profiles, including subscription upgrades, downgrades, and other changes.

  Changes:
  - Create user_profiles_history table
  - Add RLS policies for super admins to manage the table
  - This table is used by functions like upgrade_user_to_lifetime
*/

-- Create the user_profiles_history table
CREATE TABLE IF NOT EXISTS user_profiles_history (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  user_type text NOT NULL,
  subscription_status text NOT NULL,
  max_offers integer NOT NULL,
  has_analytics boolean NOT NULL,
  has_ecommerce boolean NOT NULL,
  can_change_background boolean NOT NULL,
  change_reason text NOT NULL,
  changed_at timestamptz DEFAULT now()
);

-- Enable RLS on the table
ALTER TABLE user_profiles_history ENABLE ROW LEVEL SECURITY;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_history_user_id ON user_profiles_history(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_history_changed_at ON user_profiles_history(changed_at DESC);

-- Create RLS policies for super admins
CREATE POLICY "Super admins can view all history"
  ON user_profiles_history
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.user_id = auth.uid() 
      AND up.user_type = 'super_admin'
    )
  );

CREATE POLICY "Super admins can insert history"
  ON user_profiles_history
  FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.user_id = auth.uid() 
      AND up.user_type = 'super_admin'
    )
  );

-- Allow the system to insert history records (for functions)
CREATE POLICY "System can insert history"
  ON user_profiles_history
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Verify the table was created
DO $$
BEGIN
  RAISE NOTICE 'Successfully created user_profiles_history table with RLS policies';
END $$;
