/*
  # Fix Super Admin RLS Policies for User Management

  This migration fixes the RLS policies to allow super admins to manage all user profiles,
  which is necessary for the admin dashboard functionality.

  Changes:
  - Add policy for super admins to view all user profiles
  - Add policy for super admins to update all user profiles
  - This allows the upgrade_user_to_lifetime function to work properly
*/

-- Drop existing problematic policies if they exist
DROP POLICY IF EXISTS "Super admins can view all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Super admins can manage all profiles" ON user_profiles;

-- Create policy for super admins to view all user profiles
CREATE POLICY "Super admins can view all profiles"
  ON user_profiles
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.user_id = auth.uid() 
      AND up.user_type = 'super_admin'
    )
  );

-- Create policy for super admins to update all user profiles
CREATE POLICY "Super admins can update all profiles"
  ON user_profiles
  FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.user_id = auth.uid() 
      AND up.user_type = 'super_admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.user_id = auth.uid() 
      AND up.user_type = 'super_admin'
    )
  );

-- Create policy for super admins to insert user profiles (if needed)
CREATE POLICY "Super admins can insert profiles"
  ON user_profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.user_id = auth.uid() 
      AND up.user_type = 'super_admin'
    )
  );

-- Create policy for super admins to delete user profiles (if needed)
CREATE POLICY "Super admins can delete profiles"
  ON user_profiles
  FOR DELETE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.user_id = auth.uid() 
      AND up.user_type = 'super_admin'
    )
  );

-- Verify the policies were created
DO $$
BEGIN
  RAISE NOTICE 'Successfully created super admin RLS policies for user_profiles';
END $$;
