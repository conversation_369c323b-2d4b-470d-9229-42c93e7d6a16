/*
  # Add Lifetime User Type to Check Constraint

  This migration adds 'lifetime' as a valid user_type value to the existing
  check constraint on the user_profiles table.

  Changes:
  - Drop the existing user_type check constraint
  - Recreate it to include 'lifetime' as a valid value
  - This allows users to be upgraded to lifetime plan
*/

-- First, let's check what the current constraint looks like
DO $$
BEGIN
  RAISE NOTICE 'Current user_type check constraint will be updated to include lifetime';
END $$;

-- Drop the existing check constraint
ALTER TABLE user_profiles 
DROP CONSTRAINT IF EXISTS user_profiles_user_type_check;

-- Recreate the check constraint to include 'lifetime'
ALTER TABLE user_profiles 
ADD CONSTRAINT user_profiles_user_type_check 
CHECK (user_type IN ('super_admin', 'super_unlimited', 'unlimited_yearly', 'unlimited_monthly', 'free', 'lifetime'));

-- Verify the constraint was created
DO $$
BEGIN
  RAISE NOTICE 'Successfully updated user_type check constraint to include lifetime';
END $$;
