-- Fix cascade deletes for user deletion
-- This migration adds ON DELETE CASCADE to tables that reference auth.users
-- to ensure proper cleanup when users are deleted

-- First, drop existing foreign key constraints
ALTER TABLE IF EXISTS stripe_customers 
DROP CONSTRAINT IF EXISTS stripe_customers_user_id_fkey;

-- Re-add with CASCADE
ALTER TABLE IF EXISTS stripe_customers 
ADD CONSTRAINT stripe_customers_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Add CASCADE to user_profiles if it doesn't have it
DO $$ 
BEGIN
    -- Check if the constraint exists and doesn't have CASCADE
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints tc
        JOIN information_schema.constraint_column_usage ccu ON tc.constraint_name = ccu.constraint_name
        WHERE tc.table_name = 'user_profiles' 
        AND tc.constraint_type = 'FOREIGN KEY'
        AND ccu.column_name = 'user_id'
        AND ccu.table_name = 'user_profiles'
    ) THEN
        -- Drop existing constraint
        EXECUTE 'ALTER TABLE user_profiles DROP CONSTRAINT IF EXISTS user_profiles_user_id_fkey';
        
        -- Re-add with CASCADE
        EXECUTE 'ALTER TABLE user_profiles ADD CONSTRAINT user_profiles_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE';
    END IF;
END $$;

-- Verify the changes
SELECT 
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    rc.delete_rule
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
JOIN information_schema.referential_constraints AS rc
    ON tc.constraint_name = rc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name IN ('stripe_customers', 'user_profiles')
    AND kcu.column_name = 'user_id';
