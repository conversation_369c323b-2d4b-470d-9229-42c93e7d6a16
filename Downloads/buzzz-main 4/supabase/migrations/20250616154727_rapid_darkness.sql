/*
  # User Roles and Subscription System

  1. New Tables
    - `user_profiles` - Extended user information with subscription details
    - `subscriptions` - User subscription management
    - `user_analytics` - Analytics data for users
    - `ecommerce_settings` - E-commerce configuration for super unlimited users

  2. User Types
    - super_admin: Full system control
    - super_unlimited: Unlimited + ecommerce features
    - unlimited_yearly: Unlimited features (yearly billing)
    - unlimited_monthly: Unlimited features (monthly billing)
    - free: Basic features with limitations

  3. Security
    - Enable RLS on all new tables
    - Add policies for role-based access control
    - Update existing policies to respect user roles
*/

-- Create user profiles table with subscription information
CREATE TABLE IF NOT EXISTS user_profiles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
  user_type text NOT NULL DEFAULT 'free' CHECK (user_type IN ('super_admin', 'super_unlimited', 'unlimited_yearly', 'unlimited_monthly', 'free')),
  subscription_status text DEFAULT 'active' CHECK (subscription_status IN ('active', 'inactive', 'cancelled', 'expired')),
  subscription_start_date timestamptz,
  subscription_end_date timestamptz,
  max_offers integer NOT NULL DEFAULT 3,
  has_analytics boolean NOT NULL DEFAULT false,
  has_ecommerce boolean NOT NULL DEFAULT false,
  can_change_background boolean NOT NULL DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create subscriptions table for payment tracking
CREATE TABLE IF NOT EXISTS subscriptions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  plan_type text NOT NULL CHECK (plan_type IN ('super_unlimited', 'unlimited_yearly', 'unlimited_monthly')),
  status text NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'cancelled', 'expired')),
  amount decimal(10,2) NOT NULL,
  currency text NOT NULL DEFAULT 'USD',
  billing_cycle text NOT NULL CHECK (billing_cycle IN ('monthly', 'yearly', 'lifetime')),
  stripe_subscription_id text,
  stripe_customer_id text,
  current_period_start timestamptz,
  current_period_end timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create user analytics table
CREATE TABLE IF NOT EXISTS user_analytics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  business_card_id uuid REFERENCES business_cards(id) ON DELETE CASCADE NOT NULL,
  event_type text NOT NULL CHECK (event_type IN ('card_view', 'contact_click', 'offer_click', 'social_click')),
  event_data jsonb DEFAULT '{}',
  ip_address inet,
  user_agent text,
  referrer text,
  created_at timestamptz DEFAULT now()
);

-- Create ecommerce settings table for super unlimited users
CREATE TABLE IF NOT EXISTS ecommerce_settings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
  business_card_id uuid REFERENCES business_cards(id) ON DELETE CASCADE NOT NULL,
  stripe_publishable_key text,
  stripe_secret_key text,
  paypal_client_id text,
  payment_methods jsonb DEFAULT '[]',
  tax_settings jsonb DEFAULT '{}',
  shipping_settings jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS on all new tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE ecommerce_settings ENABLE ROW LEVEL SECURITY;

-- User profiles policies
CREATE POLICY "Users can view own profile"
  ON user_profiles
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update own profile"
  ON user_profiles
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Super admins can view all profiles"
  ON user_profiles
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.user_id = auth.uid() 
      AND up.user_type = 'super_admin'
    )
  );

CREATE POLICY "Super admins can manage all profiles"
  ON user_profiles
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.user_id = auth.uid() 
      AND up.user_type = 'super_admin'
    )
  );

-- Subscriptions policies
CREATE POLICY "Users can view own subscriptions"
  ON subscriptions
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own subscriptions"
  ON subscriptions
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Super admins can view all subscriptions"
  ON subscriptions
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.user_id = auth.uid() 
      AND up.user_type = 'super_admin'
    )
  );

-- Analytics policies
CREATE POLICY "Public can insert analytics"
  ON user_analytics
  FOR INSERT
  TO anon, authenticated
  WITH CHECK (true);

CREATE POLICY "Users can view own analytics"
  ON user_analytics
  FOR SELECT
  TO authenticated
  USING (
    auth.uid() = user_id OR
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.user_id = auth.uid() 
      AND up.user_type = 'super_admin'
    )
  );

-- Ecommerce settings policies
CREATE POLICY "Super unlimited users can manage ecommerce settings"
  ON ecommerce_settings
  FOR ALL
  TO authenticated
  USING (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.user_id = auth.uid() 
      AND up.user_type IN ('super_unlimited', 'super_admin')
    )
  );

-- Function to create user profile on signup
CREATE OR REPLACE FUNCTION create_user_profile()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO user_profiles (user_id, user_type, max_offers, has_analytics, has_ecommerce, can_change_background)
  VALUES (
    NEW.id,
    'free',
    3,
    false,
    false,
    false
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create user profile on signup
DROP TRIGGER IF EXISTS create_user_profile_trigger ON auth.users;
CREATE TRIGGER create_user_profile_trigger
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION create_user_profile();

-- Function to update user profile based on subscription
CREATE OR REPLACE FUNCTION update_user_profile_from_subscription()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE user_profiles
  SET 
    user_type = NEW.plan_type,
    subscription_status = NEW.status,
    subscription_start_date = NEW.current_period_start,
    subscription_end_date = NEW.current_period_end,
    max_offers = CASE 
      WHEN NEW.plan_type IN ('super_unlimited', 'unlimited_yearly', 'unlimited_monthly') THEN -1
      ELSE 3
    END,
    has_analytics = CASE 
      WHEN NEW.plan_type IN ('super_unlimited', 'unlimited_yearly', 'unlimited_monthly') THEN true
      ELSE false
    END,
    has_ecommerce = CASE 
      WHEN NEW.plan_type = 'super_unlimited' THEN true
      ELSE false
    END,
    can_change_background = CASE 
      WHEN NEW.plan_type IN ('super_unlimited', 'unlimited_yearly', 'unlimited_monthly') THEN true
      ELSE false
    END,
    updated_at = now()
  WHERE user_id = NEW.user_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update user profile when subscription changes
DROP TRIGGER IF EXISTS update_user_profile_trigger ON subscriptions;
CREATE TRIGGER update_user_profile_trigger
  AFTER INSERT OR UPDATE ON subscriptions
  FOR EACH ROW
  EXECUTE FUNCTION update_user_profile_from_subscription();

-- Update offers table to respect user limits
ALTER TABLE offers ADD COLUMN IF NOT EXISTS order_index integer DEFAULT 0;

-- Function to check offer limits
CREATE OR REPLACE FUNCTION check_offer_limit()
RETURNS TRIGGER AS $$
DECLARE
  user_max_offers integer;
  current_offer_count integer;
BEGIN
  -- Get user's max offers limit
  SELECT max_offers INTO user_max_offers
  FROM user_profiles
  WHERE user_id = NEW.user_id;
  
  -- If unlimited (-1), allow creation
  IF user_max_offers = -1 THEN
    RETURN NEW;
  END IF;
  
  -- Count current active offers for this user
  SELECT COUNT(*) INTO current_offer_count
  FROM offers
  WHERE user_id = NEW.user_id AND is_active = true;
  
  -- Check if limit would be exceeded
  IF current_offer_count >= user_max_offers THEN
    RAISE EXCEPTION 'Offer limit exceeded. Your plan allows maximum % offers.', user_max_offers;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to check offer limits on insert/update
DROP TRIGGER IF EXISTS check_offer_limit_trigger ON offers;
CREATE TRIGGER check_offer_limit_trigger
  BEFORE INSERT OR UPDATE ON offers
  FOR EACH ROW
  WHEN (NEW.is_active = true)
  EXECUTE FUNCTION check_offer_limit();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_type ON user_profiles(user_type);
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_user_analytics_user_id ON user_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_user_analytics_business_card_id ON user_analytics(business_card_id);
CREATE INDEX IF NOT EXISTS idx_user_analytics_event_type ON user_analytics(event_type);
CREATE INDEX IF NOT EXISTS idx_user_analytics_created_at ON user_analytics(created_at);
CREATE INDEX IF NOT EXISTS idx_ecommerce_settings_user_id ON ecommerce_settings(user_id);